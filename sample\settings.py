"""
Django settings for demo project.

Generated by 'django-admin startproject' using Django 3.0.3.

For more information on this file, see
https://docs.djangoproject.com/en/3.0/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/3.0/ref/settings/
"""

import os

# Build paths inside the project like this: os.path.join(BASE_DIR, ...)
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
TEMPLATE_DIR = os.path.join(BASE_DIR, 'templates')

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/3.0/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'musuP(9j)qMi.bRh_OjT&rEQNGq}&uL3T&Bo&;<n6O?3Sl?n&Q'

GOOGLE_RECAPTCHA_SECRET_KEY = '6LexnEIaAAAAAElq_moEGYcrnczEw1pxIHHmhLiX'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True  # it was false  = jasmeet
ALLOWED_HOSTS = ['*']


# Application definition
INSTALLED_APPS = [
    'django_crontab', # pip install django-crontab
    ######## Django apps ################
     'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.sites',
    'django.contrib.sitemaps',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',


    ######### Third Party Apps ############
    'ckeditor',  # pip install django-ckeditor
    'ckeditor_uploader',  # pip install django-ckeditor
    'robots', # pip install django-robots
    'sorl.thumbnail', # pip install sorl-thumbnail
    'crispy_forms',  # pip install --upgrade django-crispy-forms
    'django_user_agents', # pip install pyyaml ua-parser user-agents,# pip install django-user-agents
    'dbbackup',  # pip install django-dbbackup
    'django_summernote', # pip install django-summernote
    # 'faicon', # pip install django-faicon

    'allauth', # pip install django-allauth
    'allauth.account',
    'allauth.socialaccount',

    # All Auth Providers
    'allauth.socialaccount.providers.google',
    # 'allauth.socialaccount.providers.facebook',   # not in use
    ######### My Apps #####################
    'app',
    'blog',
    'accounts',
    'aggrement',
    'user_profile',
    'law_library',
    'contact_us',
    'legal_dictionary',
    'faq',
    'contract_seo',
    'shorturl',
    'email_configuration',
    'services',
    # 'price',
    'talk_to_lawyer',
    'django.contrib.humanize',
]



SITE_ID = 1

THUMBNAIL_QUALITY = 60

THUMBNAIL_PROGRESSIVE = False

CKEDITOR_UPLOAD_PATH = "uploads/"

X_FRAME_OPTIONS = 'SAMEORIGIN'

CKEDITOR_CONFIGS = {
    'special': {
        'toolbar': 'Special',
        'width': 'auto',
        'toolbar_Special': [
            ['Styles', 'Font','Format', 'Bold', 'Italic', 'Underline', 'Strike', 'SpellChecker', 'Undo'],
            ['Link', 'Unlink', 'Anchor','JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock'],
            ['Image', 'Flash', 'Table', 'HorizontalRule'],
            ['TextColor', 'BGColor'],
            ['Smiley', 'SpecialChar'], ['Source'],
        ],

    }
}



SESSION_ENGINE = 'django.contrib.sessions.backends.db'  # default

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',

    ############### Other MiddleWare #######################

    'django_user_agents.middleware.UserAgentMiddleware',
]

ROOT_URLCONF = 'sample.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [TEMPLATE_DIR,],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'contact_us.context_processors.contact_us_details', # from contact_us app
                'services.context_processors.service_category_list',  # from services app
                'blog.context_processors.blog_category_list' # from blog app
            ],
        },
    },
]

WSGI_APPLICATION = 'sample.wsgi.application'


# Database
# https://docs.djangoproject.com/en/3.0/ref/settings/#databases

# DATABASES = {
#     'default': {
#         'ENGINE': 'django.db.backends.postgresql',
#         'NAME': 'contract-easy',
#         'USER': 'postgres',
#         'PASSWORD': 'qwerty123',
#         'HOST': 'localhost',
#         'PORT': '5432',
#     }
# }

# DATABASES = {
#     'default': {
#         'ENGINE': 'django.db.backends.mysql',
#         'NAME': 'contract_easily',
#         'USER': 'root',
#         'PASSWORD': '',
#         'HOST': 'localhost',
#         'PORT': '3306',
#     }
# }

# ============ jasmeet commented
# DATABASES = {
#     'default': {
#         'ENGINE': 'django.db.backends.mysql',
#         'NAME': 'adbozhqi_contracteasily',
#         'USER': 'adbozhqi_contracteasily',
#         'PASSWORD': 'Contracteasily',
#         'HOST': 'localhost',
#         'PORT': '3306',
#     }
# }

# ============= jasmeet added
# DATABASES = {
#     'default': {
#         'ENGINE': 'django.db.backends.sqlite3',
#         'NAME': os.path.join(BASE_DIR, 'db.sqlite3'),
#     }
# }

# ============= Database Configuration
# Using SQLite for development
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': os.path.join(BASE_DIR, 'db.sqlite3'),
    }
}






# Password validation
# https://docs.djangoproject.com/en/3.0/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/3.0/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'Asia/Kolkata'

USE_I18N = True

USE_L10N = True

USE_TZ = True

CRISPY_TEMPLATE_PACK = 'bootstrap4'

SENDGRID_API_KEY='*********************************************************************'
EMAIL_HOST = 'smtp.sendgrid.net'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = 'apikey'
EMAIL_HOST_PASSWORD = '*********************************************************************'
DEFAULT_FROM_EMAIL = '<EMAIL>'
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/3.0/howto/static-files/

STATIC_URL = '/static/' 

STATICFILES_DIRS = [
    os.path.join(BASE_DIR, "static"),
]

STATIC_ROOT = os.path.join(BASE_DIR, "static-root")

MEDIA_URL = '/media/'

MEDIA_DIR = os.path.join(BASE_DIR, 'media')

MEDIA_ROOT = os.path.join(BASE_DIR, 'media')


# STATIC_URL = '/static/'
# STATIC_ROOT = '/home/<USER>/contracteasily.com/static'

# MEDIA_URL = '/media/'
# MEDIA_ROOT = '/home/<USER>/contracteasily.com/media/'


LOGIN_URL = 'accounts:login'
LOGOUT_URL = 'accounts:logout'
LOGIN_REDIRECT_URL = "index"
LOGOUT_REDIRECT_URL = "index"



# Test
PHONEPE_API = "test" # "test" # or "production"  # added
MERCHANT_ID = "MERCHANTUAT" 
SALT_KEY_INDEX = "1"
SALT_KEY = "099eb0cd-02cf-4e2a-8aca-3e6c6aff0399" 


# # Phonepe Keys for production
# MERCHANT_ID = "CONTRACTONLINE" 
# SALT_KEY_INDEX = 1
# SALT_KEY = "7aaf6df6-0124-457b-8675-d76822f7dd92"



OPEN_AI_KEY = '***************************************************'

SESSION_COOKIE_AGE = 1209600  # 2 weeks in seconds


# ======================== Social Authentication Google ===============================
# AUTHENTICATION_BACKENDS = (
#     'django.contrib.auth.backends.ModelBackend',  # default
#     # 'allauth.account.auth_backends.AuthenticationBackend',
# )

ACCOUNT_EMAIL_REQUIRED = True
ACCOUNT_USERNAME_REQUIRED = False
ACCOUNT_AUTHENTICATION_METHOD = 'email'
ACCOUNT_EMAIL_VERIFICATION = 'optional'
# LOGIN_REDIRECT_URL = '/'
# LOGOUT_REDIRECT_URL = '/'

SOCIALACCOUNT_PROVIDERS = {
    'google': {
        'SCOPE': ['profile', 'email'],
        'AUTH_PARAMS': {'access_type': 'online'},
    }
}

SOCIALACCOUNT_LOGIN_ON_GET = True # If set it false, while login using google - it will redirect to a page. That page shows a menu wih options of "sign-up" and "sign-in" and "continue" button. We used to click this "continue" button to login our gmail account. So in order to skip this one step, it is set to True. By setting True, it directly checks for gmail account.


CRONJOBS = [ # '0 6 * * *' at 6 am daily    '*/2 * * * *' at every 2 minutes
    # ('0 10 * * *', 'email_configuration.cron.send_agreement_order_pending_email_scheduled_job'),  # want to Run daily at 10:00 am Indian time but it run at 7:30pm Indian time
    # ('20 11 * * *', 'email_configuration.cron.send_agreement_order_pending_email_scheduled_job'),  # Run daily at 11:20 AM
    # ('0 10 * * *', 'email_configuration.cron.send_service_order_pending_email_scheduled_job'), # want to Run daily at 10:00 am Indian time but it run at 7:30pm Indian time
    
    # ('30 4 * * *', 'email_configuration.cron.send_agreement_order_pending_email_scheduled_job'),  # it run at 2:00 pm Indian Time
    # ('30 4 * * *', 'email_configuration.cron.send_service_order_pending_email_scheduled_job'), # it run at 2:00 pm Indian Time
    
    ('30 0 * * *', 'email_configuration.cron.send_agreement_order_pending_email_scheduled_job'),  # it run at 10:00 am Indian Time
    ('30 0 * * *', 'email_configuration.cron.send_service_order_pending_email_scheduled_job'), # it run at 10:00 am Indian Time
    
]


import logging
import os
APP_LOG_FILENAME = os.path.join(BASE_DIR, 'logs/app.log' )
ERROR_LOG_FILENAME = os.path.join(BASE_DIR, 'logs/error.log')
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters' : {
        'console': {
            'format': '%(name) - 12s %(levelname) -8s %(message)s'
        },
        'file': {
            'format': '%(asctime)s %(name) - 12s %(levelname) -8s %(message)s'
        }
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'console'
        },
        'file': {
            'level' : 'DEBUG',
            'class' : 'logging.FileHandler',
            'formatter' : 'file',
            'filename' : APP_LOG_FILENAME
        }
    },
    'loggers': {
        '' : {
            'level': 'DEBUG',
            'handlers': ['console', 'file']
        }
    }
}


# SUMMERNOTE_CONFIG = {
#     'summernote': {
#         # Other configurations...
#         # Include CDN links for additional resources
#         'css': (
#             '//maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css'
           
#         ),
       
#     },
# }
