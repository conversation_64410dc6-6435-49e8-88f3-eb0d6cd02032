.row {
    margin-right: inherit;
    margin-left: inherit;
}


/* ================= Checkout html ==================
================================================== */
label {
    display: block;
    line-height: 1;
    margin-bottom: 10px;
    font-weight: 400;
    text-transform: capitalize;
}

input[type="text"]::placeholder,
input[type="email"]::placeholder 
  {
    color: #999999; /* You can replace this with your desired color code or name */
  }
/*Input Field & Select*/


input:not([type=submit]):not([type=checkbox]):not([type=radio]):not([type=file]),
textarea {
    border: none;
    -webkit-box-shadow: none;
    box-shadow: none;
    color: #999999;
    border-radius: 0;
    background-color: #f7f7f7;
    margin-bottom: 0;
    padding: 10px 25px;
    max-width: 100%;
    width: 100%;
    font-size: 14px;
    line-height: 30px;
    font-weight: 300;
    outline: none;
}  


select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-image: url(../images/app_icon/select-arrow-down.png);
    background-position: calc(100% - 25px) 50%;
    background-repeat: no-repeat;
    font-family: "Poppins", sans-serif;
    width: 100%;
    border: none;
    background-color: #f7f7f7;
    line-height: 24px;
    font-weight: 300;
    height: 50px;
    padding: 10px 44px 10px 21px;
    color: #666666;
    border-radius: 0;
    max-width: 500px;
}


/*Custom Checkbox*/
.cus-checkbox-wrap {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    margin: -5px -10px;
}

.cus-checkbox-wrap.inline {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
}

.cus-checkbox-wrap .cus-checkbox {
    margin: 5px 10px;
}

.cus-checkbox {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    margin: 0;
}

.cus-checkbox input {
    position: absolute;
    left: -9999px;
    top: 0;
    opacity: 0;
    width: 0;
    height: 0;
    visibility: hidden;
}

.cus-checkbox input:checked+label::before {
    background-color: #c89a69;
    border-color: #c89a69;
    background-image: url(../images/app_icon/checkbox.png);
}

.cus-checkbox input:disabled+label {
    opacity: 0.5;
    cursor: not-allowed;
}

.cus-checkbox label {
    display: inline-block;
    line-height: 22px;
    position: relative;
    padding-left: 28px;
    min-width: 22px;
    min-height: 22px;
    cursor: pointer;
    margin: 0;
}

.cus-checkbox label span {
    padding-left: 8px;
}

.cus-checkbox label::before {
    content: "";
    position: absolute;
    left: 0;
    top: 1px;
    width: 18px;
    height: 18px;
    border: 2px solid #bbbbbb;
    border-radius: 2px;
    -webkit-transition: all 0.3s ease 0s;
    transition: all 0.3s ease 0s;
    background-position: center center;
    background-repeat: no-repeat;
}

.cus-checkbox.primary input:checked+.icon {
    background-color: #c89a69;
    border-color: #c89a69;
}

.cus-checkbox.secondary input:checked+.icon {
    background-color: #6c757d;
    border-color: #6c757d;
}

.cus-checkbox.success input:checked+.icon {
    background-color: #4CAF50;
    border-color: #4CAF50;
}

.cus-checkbox.danger input:checked+.icon {
    background-color: #F44336;
    border-color: #F44336;
}

.cus-checkbox.warning input:checked+.icon {
    background-color: #FFC107;
    border-color: #FFC107;
}

.cus-checkbox.info input:checked+.icon {
    background-color: #17a2b8;
    border-color: #17a2b8;
}

.bg-gray {
    background-color: #f8f8f8 !important;
}


.asset-button-4 button {
    /* color: #333333!important;  */
    color: #000 !important;
    /* added */
    font-family: 'Open Sans', sans-serif;
    font-size: 20px;
    font-weight: bold;
    padding: 17px 26px;
    /* border: 2px solid #eee!important;  */
    border: 2px solid #d5aa6d !important;
    /* added */
    /*background: #fff!important; */
    background: #d5aa6d !important;
    /* added */
    line-height: 1;
    border-radius: 0;
    outline: 0;
    box-shadow: none;
    -webkit-transition: all 300ms linear 0ms;
    -o-transition: all 300ms linear 0ms;
    -mz-transition: all 300ms linear 0ms;
    transition: all 300ms linear 0ms;
}

.asset-button-4 button:hover,
.asset-button-4 button:active,
.asset-button-4 button:focus,
.asset-button-4 button:visited {
    outline: none;
    text-decoration: none;
    color: #d5aa6d !important;
    background-color: #000 !important;
    border-color: #000 !important;
    -webkit-transition: all 300ms linear 0ms;
    -o-transition: all 300ms linear 0ms;
    -mz-transition: all 300ms linear 0ms;
    transition: all 300ms linear 0ms;
    box-shadow: none;
}


.custom-flex {
    display: flex;
    align-items: center;
}

.custom-flex strong {
    margin-right: 10px;
}


/*Custom Radio*/
.cus-radio-wrap {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    margin: -5px -10px;
  }
  .cus-radio-wrap.inline {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-direction: row;
            flex-direction: row;
  }
  .cus-radio-wrap .cus-radio {
    margin: 5px 10px;
  }
  
  .cus-radio {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    margin: 0;
    -webkit-transition: all 0.3s ease 0s;
    transition: all 0.3s ease 0s;
  }
  .cus-radio input {
    position: absolute;
    left: -9999px;
    top: 0;
    opacity: 0;
    width: 0;
    height: 0;
    visibility: hidden;
    margin: 0;
  }
  .cus-radio input:checked + label::before {
    background-color: #c89a69;
  }
  .cus-radio input:checked + label::after {
    -webkit-transform: scale(1);
            transform: scale(1);
    opacity: 1;
  }
  .cus-radio input:disabled + label {
    opacity: 0.5;
    cursor: not-allowed;
  }
  .cus-radio label {
    display: inline-block;
    line-height: 22px;
    position: relative;
    padding-left: 28px;
    min-width: 22px;
    min-height: 22px;
    cursor: pointer;
    margin: 0;
  }
  .cus-radio label span {
    padding-left: 8px;
  }
  .cus-radio label::before {
    content: "";
    width: 18px;
    height: 18px;
    display: block;
    border-radius: 50%;
    position: absolute;
    left: 0;
    top: 2px;
    background-color: #cccccc;
    -webkit-transition: all 0.3s ease 0s;
    transition: all 0.3s ease 0s;
  }
  .cus-radio label::after {
    content: "";
    display: block;
    border-radius: 50%;
    position: absolute;
    left: 5px;
    top: 7px;
    width: 8px;
    height: 8px;
    background-color: #ffffff;
    opacity: 0;
    -webkit-transform: scale(3);
            transform: scale(3);
    -webkit-transition: all 0.3s ease 0s;
    transition: all 0.3s ease 0s;
  }
  .cus-radio.primary input:checked + .icon {
    background-color: #c89a69;
  }
  .cus-radio.secondary input:checked + .icon {
    background-color: #6c757d;
  }
  .cus-radio.success input:checked + .icon {
    background-color: #4CAF50;
  }
  .cus-radio.danger input:checked + .icon {
    background-color: #F44336;
  }
  .cus-radio.warning input:checked + .icon {
    background-color: #FFC107;
  }
  .cus-radio.info input:checked + .icon {
    background-color: #17a2b8;
  }
  

  .invalid-input {
    border: 1px solid #a94442 !important;
    background-position: right center;
    background-repeat: no-repeat;
    padding-right: 20px;
}


/* Set the Font Awesome cross icon as a background image */
.input-with-icon {
    /* background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"/></svg>'); */
    background-image: url('data:image/svg+xml;charset=utf-8,%3Csvg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"%3E%3Cpath d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z" fill="%23a94442"%3E%3C/path%3E%3C/svg%3E');

    background-position: right center; /* Position the icon on the right center */
    background-repeat: no-repeat; /* Prevent the icon from repeating */
    background-size: 20px; /* Set the size of the icon */
  }
/* Set the Font Awesome cross icon color to blue using the color property */



