"""demo URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/3.0/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path
from app import views
from django.conf.urls.static import static
from django.conf import settings
from django.conf.urls import url, include
from django.contrib.sitemaps.views import sitemap

####### Sitemaps Import ######################
from app.sitemaps import HomeViewSiteMap, AllLawyersSiteMap, TermsSiteMap, PrivacySiteMap, DisclaimerSiteMap
from aggrement.sitemaps import AgreementListSiteMap, MyAgreementSiteMap
from blog.sitemaps import BlogViewSiteMap, AllBlogSiteMap, AllBlogCategoriesSiteMap, AllBlogTagsSiteMap
from contact_us.sitemaps import ContactUsSiteMap
from faq.sitemaps import FaqSiteMap
from law_library.sitemaps import AllActsSiteMap, AllActDetailsSiteMap
from legal_dictionary.sitemaps import LegalDictionarySiteMap

sitemaps = {
    
    'home' : HomeViewSiteMap,
    'attorneys' : AllLawyersSiteMap,
    'terms' : TermsSiteMap,
    'privacy' : PrivacySiteMap,
    'disclaimer' : DisclaimerSiteMap,
    'all_agreements' : AgreementListSiteMap,
    'my_agreements' : MyAgreementSiteMap,
    'blog_list' : BlogViewSiteMap,
    'all_blogs' : AllBlogSiteMap,
    'blog_categories' : AllBlogCategoriesSiteMap,
    'blog_tags' : AllBlogTagsSiteMap, 
    'contact_us' : ContactUsSiteMap,
    'faq' : FaqSiteMap,
    'all_acts' : AllActsSiteMap,
    'act_details' : AllActDetailsSiteMap,
    'legal_dictionary' : LegalDictionarySiteMap   
}

handler404 = 'app.views.my_custom_permission_denied_view'
handler500 = 'app.views.my_custom_server_error_view'

urlpatterns = [

    path('admin/', admin.site.urls),

	path('', views.index, name='index'),

    path('dashboard/master/', views.admin_dashboard, name='admin_dashboard'),
    path('dashboard/master/chats/', views.admin_dashboard_make_with_ai_user_chats, name='admin_dashboard_make_with_ai_user_chats'),
    path('dashboard/master/chat/<int:chat_id>/', views.admin_dashboard_make_with_ai_user_chat_detail, name='admin_dashboard_make_with_ai_user_chat'),
    path('dashboard/master/change-price/<str:agreement_type>/', views.admin_dashboard_change_agreement_price, name='admin_dashboard_change_agreement_price'),
    path('dashboard/master/orders/<str:order_type>/', views.admin_dashboard_orders, name='admin_dashboard_orders'),
    path('dashboard/master/order/<str:order_type>/<int:order_id>', views.admin_dashboard_order_detail, name='admin_dashboard_order_detail'),
    path('dashboard/master/agreement/<int:agreement_id>', views.admin_dashboard_agreement_detail, name='admin_dashboard_agreement_detail'),
    path('dashboard/master/service/<int:service_id>', views.admin_dashboard_service_detail, name='admin_dashboard_service_detail'),
    path('dashboard/master/services/', views.ServiceListViewAdmin.as_view(), name='admin_dashboard_services'),
    path('dashboard/master/autocomplete', views.autocomplete, name='autocomplete'),
    path('dashboard/master/search-dropdown', views.search_dropdown, name='search_dropdown'),
    path('dashboard/master/search', views.search_results, name='search_results'),
    path('dashboard/master/most-used-agreements', views.admin_dashboard_get_most_used_legal_agreements, name='admin_dashboard_get_most_used_legal_agreements'),
    path('dashboard/master/change-order-status/<str:order_type>/<int:order_id>',views.admin_dashboard_change_order_status_for_order, name='admin_dashboard_change_order_status_for_order'),
    path('dashboard/master/change-email-sequence-days', views.admin_dashboard_change_email_sequence_days_to_send_pending_orders_email, name='admin_dashboard_change_email_sequence_days'),
    path('get-orders-for-sales-notifications/', views.get_orders_for_sales_notifications, name="get_orders_for_sales_notifications"),
    # ================= EXTRA =================
    # path('dashboard/master/reset/', views.reset_order_status),
    # ================= EXTRA =================
    
    
    # path('e-contracts/', views.e_contract, name='contracts'),
    path('accounts/social/signup/', views.CustomSignupView.as_view(), name='socialaccount_signup'),   # added
    path('accounts/', include('allauth.urls')),
    
    
    path('cancellation-and-refund-policy/', views.cancellation_and_refund_policy, name='cancellation_and_refund_policy'),  # added
    path('attorney/', views.attorney, name='attorney'),
    url(r'^attorney_details/(?P<attorney_slug>[\w-]+)/$', views.attorney_details, name='attorney_details'),

    url(r'^price-list/$', views.price_list, name='price_list'),

    
    path('terms-and-conditions/', views.terms_and_conditions, name='terms_and_conditions'),
    path('privacy-policy/', views.privacy_policy, name='privacy_policy'),
    path('disclaimer/', views.disclaimer, name='disclaimer'),
    # path('how-to-onboard-as-a-lawyer/', views.lawyer_onboard, name='lawyer_onboard'), # not in use

    path('custom-login', views.custom_login_view, name='custom_login'),
    path('custom-login-redirect', views.custom_login_redirect, name='custom_login_redirect'),

    path('ckeditor/', include('ckeditor_uploader.urls')),
    path('summernote/', include('django_summernote.urls')),
    path('faicon/', include('faicon.urls')),

    url(r'^sitemap.xml/$', sitemap, {'sitemaps' : sitemaps} , name='sitemap'),
    url(r'^robots.txt', include('robots.urls')),

    ############# App Urls ################################
    url(r"^email-configuration/", include("email_configuration.urls", namespace="email_configuration")),

    url(r"^legal-services/", include("services.urls", namespace="services")),
    url(r"^contract-accounts/", include("accounts.urls", namespace="accounts")),
    url(r"^legal-documents/", include("aggrement.urls", namespace="aggrement")),
    url(r"^blogs/", include("blog.urls", namespace="blog")),
    url(r"^contact-us/", include("contact_us.urls", namespace="contact_us")),
    
    url(r"^law-library/", include("law_library.urls", namespace="law_library")),
    url(r"^frequently-asked-questions/", include("faq.urls", namespace="faq")),
    url(r"^legal-dictionary/", include("legal_dictionary.urls", namespace="legal_dictionary")),
    url(r"^document/", include("shorturl.urls", namespace="shorturl")),
    url(r"^user/profile/", include("user_profile.urls", namespace="user_profile")),
    


] + static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

