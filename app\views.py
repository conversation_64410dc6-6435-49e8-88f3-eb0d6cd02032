import random
import threading
from django.shortcuts import render, redirect
from django.db.models import Count, Value
from django.contrib import messages
from django.urls import reverse
from django.http import HttpResponseRedirect, JsonResponse
from django.db.models.functions import Coalesce
from django.contrib.auth.decorators import login_required
from django.core.paginator import <PERSON><PERSON>ator, EmptyPage, PageNotAnInteger

from aggrement.views import get_agreement_orders_by_state, get_earnings_for_year, get_ordered_agreements_guest_users_count, get_total_agreement_count, get_total_orders_count, repeated_agreement_orders_by_user, send_email_for_completed_order_status_for_agreement_order
from email_configuration.models import WelcomeMail
from user_profile.views import get_users_signup_count_for_year
from services.views import get_ordered_getQuote_services_guest_users_count, get_ordered_services_guest_users_count, get_service_earnings_for_year, get_service_get_quote_orders_count_for_year, get_service_orders_by_state, get_total_service_count, get_total_service_get_quote_orders_data, get_total_service_orders_data, repeated_service_orders_by_user, send_email_for_order_completed_service_order
from services.models import GetQuoteService, HomePageServices, OrderService, Services, ServicesCategory
from .common import CommonMixin
from aggrement.models import Aggrement, AggrementCatagories, Chat, OrderAgreement, UserAggrements, UserOrder
from blog.models import Blog
from contract_seo.models import CommonSeo
from user_profile.models import Profile, LawyerSubCategory
from email_configuration.views import query_legal_help_mail, query_legal_help_admin_mail
from contact_us.forms import QueryFromLegalhelpForm
from contact_us.models import QueryFromLegalhelp,City
from .models import CountryMaster, StateMaster, TermsAndConditions, PrivacyPolicy, Disclaimer, Testimonials, LawyerOnboardSteps, SubscribeNewsletterForm
from allauth.socialaccount.views import SignupView
from django.contrib import messages
from django.db.models import Q
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import ListView
from django.contrib.auth.models import User
from django.template.loader import render_to_string
import urllib.parse
from django.db.models import Value, CharField, F, Func, Aggregate
from django.db.models.functions import Coalesce, Concat


def index(request):
	"""
	Index View 
	"""

	blog_list = Blog.objects.all().order_by('-id')[:4]

	aggrement_list = Aggrement.objects.all().order_by('?')[:5]

	aggrement_category_list = AggrementCatagories.objects.all().order_by('?')[:16]  # categories number changed from 8 to 16

	testimonial_list = Testimonials.objects.all().order_by('-id')

	seo = CommonSeo.objects.filter(sample__exact='Index-Page-Seo').first()

	agreement_count = Aggrement.objects.all().aggregate(
			partial_total=Coalesce(Count('id'), Value(0)))['partial_total']  # Total Document Created

	documents_created = UserAggrements.objects.all().aggregate(
			partial_total=Coalesce(Count('id'), Value(0)))['partial_total']  # Total Document Created

	user_count = Profile.objects.all().aggregate(
			partial_total=Coalesce(Count('id'), Value(0)))['partial_total']  # Total Document Created

	experts_count = Profile.objects.filter(user_type='LAWYER').aggregate(
			partial_total=Coalesce(Count('id'), Value(0)))['partial_total']  # Total Document Created
 
	practice_images = ['practice-1.jpg', 'practice-2.jpg', 'practice-3.jpg', 'practice-4.jpg','practice-5.jpg', 'practice-6.jpg', 'practice-7.jpg', 'practice-8.jpg']
	tmlawyer_icons = ['tmlawyer-car', 'tmlawyer-money', 'tmlawyer-square', 'tmlawyer-star', 'tmlawyer-docs', 'tmlawyer-book', 'tmlawyer-key', 'tmlawyer-eight']
   
	practice_icons = ['fa fa-child', 'fa fa-fire', 'fa fa-shopping-cart', 'fa fa-female', 'fa fa-pagelines', 'fa fa-money', 'fa fa-umbrella', 'fa fa-bank'] 
	countries = CountryMaster.objects.all()
	states = StateMaster.objects.all()
 
	homepage_service_obj = HomePageServices.objects.filter(id=1).first()
 
	context = {
		'blog_list' : blog_list,
		'aggrement_list' : aggrement_list,
		'aggrement_category_list' : aggrement_category_list,
		'testimonial_list' : testimonial_list,
		'seo' : seo,
		'agreement_count' : agreement_count,
		'documents_created' : documents_created,
		'user_count' : user_count,
		'experts_count' : experts_count,
		"practice_images" : practice_images,
		"tmlawyer_icons" : tmlawyer_icons,
		"practice_icons": practice_icons,
		"countries": countries,
		"states": states,
		"homepage_service_obj": homepage_service_obj
	}
	return CommonMixin.render(request, 'index.html', context)


def custom_login_view(request):
	"""
	View To Define Login View
	"""
	return redirect("accounts:login")


def custom_login_redirect(request):
	"""
	Custom Login Redirect
	"""
	return redirect("index")


def e_contract(request):
	context = {}
	return CommonMixin.render(request, 'e_contract.html', context)

def attorney(request):
	"""
	Lawyer List
	"""

	lawyers = Profile.objects.filter(user_type='LAWYER').order_by('user__username')
	page = request.GET.get('page', 1)
	
	city = City.objects.all()

	paginator = Paginator(lawyers, 16)
	try:
		lawyer_list = paginator.page(page)
	except PageNotAnInteger:
		lawyer_list = paginator.page(1)
	except EmptyPage:
		lawyer_list = paginator.page(paginator.num_pages)

	seo = CommonSeo.objects.filter(sample__exact='LAWYER-PAGE-SEO').first()

	sub_category_list = LawyerSubCategory.objects.all().order_by('id')

	context = {
		'city' : city,
		'lawyer_list' : lawyer_list,
		'seo' : seo,
		'sub_category_list' : sub_category_list
	}
	return CommonMixin.render(request, 'attorney.html', context)


def price_list(request):
	"""
		Price list page
	"""
	context ={

	}
	return render(request,'price.html',context)



def attorney_details(request, attorney_slug):
	"""
	Lawyer Details
	"""
	attorney_details = Profile.objects.filter(slug=attorney_slug).first()

	if request.method == "POST":
		query_form = QueryFromLegalhelpForm(request.POST or None)
		if query_form.is_valid():
			name = request.POST.get('name')
			email = request.POST.get('email')
			phone_no = request.POST.get('phone_no')
			message = request.POST.get('message')

			# Begin reCAPTCHA validation
			# recaptcha_response = request.POST.get('g-recaptcha-response')
			# url = 'https://www.google.com/recaptcha/api/siteverify'
			# values = {
			# 	'secret': settings.GOOGLE_RECAPTCHA_SECRET_KEY,
			# 	'response': recaptcha_response
			# }
			# data = urllib.parse.urlencode(values).encode()
			# req =  urllib.request.Request(url, data=data)
			# response = urllib.request.urlopen(req)
			# result = json.loads(response.read().decode())
			# End reCAPTCHA validation

			answer = QueryFromLegalhelp.objects.create(
				lawyer=attorney_details,name=name, email=email, phone_no=phone_no, message=message)
			answer.save()
			query_legal_help_mail(lawyer_help=answer)
			query_legal_help_admin_mail(lawyer_help=answer)
			messages.success(
			request, 'Your Message is send successfully to ' + attorney_details.full_name)

			return HttpResponseRedirect(request.META.get("HTTP_REFERER"))
	else:
		query_form = QueryFromLegalhelpForm()

	seo = CommonSeo.objects.filter(sample__exact='LAWYER-PAGE-SEO').first()


	context = { 
		'attorney_details' : attorney_details,
		'legal_help_form' : query_form,
		'seo' : seo
	}
	return CommonMixin.render(request, 'attorney_details.html', context)


def terms_and_conditions(request):
	"""
	Terms And Conditions
	"""
	terms = TermsAndConditions.objects.filter(sample__exact='TERMS-AND-CONDITIONS').first()

	context = {
		'terms' : terms
	}
	return CommonMixin.render(request, 'terms_and_conditions.html', context)

def privacy_policy(request):
	"""
	Privacy Policy View
	"""
	privacy = PrivacyPolicy.objects.filter(sample__exact='PRIVACY-POLICY').first()

	context = {
		'privacy' : privacy
	}
	return CommonMixin.render(request, 'privacy.html', context)

def disclaimer(request):
	"""
	Privacy Policy View
	"""
	disclaimer = Disclaimer.objects.filter(sample__exact='DISCLAIMER').first()

	context = {
		'disclaimer' : disclaimer
	}
	return CommonMixin.render(request, 'disclaimer.html', context)

def lawyer_onboard(request):
	"""
	Lawyer Onboard
	"""
	onboard_details = LawyerOnboardSteps.objects.filter(sample__exact='LAWYER-ONBOARD-STEPS').first()

	seo = CommonSeo.objects.filter(sample__exact='LAWYER-ONBOARD-STEPS').first()

	context = {
		'onboard_details' : onboard_details,
		'seo' : seo
	}
	return CommonMixin.render(request, 'onboard_details.html', context)


def my_custom_permission_denied_view(request, exception): # ,exception
	"""
	 404 Page  View
	"""
	template_name = '404.html'

	context = {

	}
	return CommonMixin.render(request, template_name ,context)



def my_custom_server_error_view(request, *args, **argv):
	"""
	500 Page  View
	"""
	template_name = '500.html'

	return render(request, template_name)


class CustomSignupView(SignupView):
	def get(self, request, *args, **kwargs):
		   return redirect(reverse('accounts:login'),
								messages.error(request, f"Our records show that you already have an account with this email. Please login with ContractEasily to access your account or use other email.",
												))



########################## Admin Views ############################

@login_required
def admin_dashboard(request):
	"""
	Admin Dashboard
	"""
	try:
		if not request.user.is_superuser:
			messages.error(request, 'You have no permission to access the requested resource!')
			return redirect(reverse('index'))
	except AttributeError as error:
		messages.error(request, 'You have no permission to access the requested resource!')
		return redirect(reverse('index'))


	# Get agreement earnings data of a last year
	users_signup_count_data = get_users_signup_count_for_year()
	# Prepare agreement earnings data for Chart.js
	users_signup_count_labels = list(users_signup_count_data.keys())
	users_signup_count_data = list(users_signup_count_data.values())

	context = {}
	context['users_signup_count_labels'] = users_signup_count_labels
	context['users_signup_count_data'] = users_signup_count_data

	# Get agreement earnings data of a last year
	earnings_data = get_earnings_for_year()

	# Prepare agreement earnings data for Chart.js
	earning_labels = list(earnings_data.keys())
	earning_data = list(earnings_data.values())

	context['earning_labels'] = earning_labels
	context['earning_data'] = earning_data

	# Get agreement earnings data of a last year
	service_earnings_data = get_service_earnings_for_year()

	# Prepare service order earnings data for Chart.js
	service_earning_labels = list(service_earnings_data.keys())
	service_earning_data = list(service_earnings_data.values())

	context['service_order_earning_labels'] = service_earning_labels
	context['service_order_earning_data'] = service_earning_data

	# Get service-Get-A-Quote orders count of a last year
	service_quote_order_data = get_service_get_quote_orders_count_for_year()
	# Prepare service-Get-A-Quote order count for Chart.js
	service_quote_count_labels = list(service_quote_order_data.keys())
	service_quote_count_data = list(service_quote_order_data.values())

	context['service_quote_order_count_labels'] = service_quote_count_labels
	context['service_quote_order_count_data'] = service_quote_count_data

	# Get agreement orders counts
	orders_counts = get_total_orders_count()
	context['agreement_orders_count_data'] = orders_counts
	
	# Get service orders counts
	total_service_orders_data = get_total_service_orders_data()
	context['total_service_orders_data'] = total_service_orders_data
	
	# Get service-Get A Quote orders counts
	total_service_get_quote_orders_data = get_total_service_get_quote_orders_data()
	context['total_service_get_quote_orders_data'] = total_service_get_quote_orders_data
	
	agreement_orders_state_data = get_agreement_orders_by_state()
	context['agreement_orders_state_data'] = agreement_orders_state_data

	service_orders_state_data = get_service_orders_by_state()
	context['service_orders_state_data'] = service_orders_state_data

	total_agreement_count = get_total_agreement_count()
	context['total_agreement_count'] = total_agreement_count 

	total_service_count = get_total_service_count()
	context['total_service_count'] = total_service_count


	# Total Users 
	total_users_count = {}

	total_users_count['total_signedup_users_count'] = User.objects.all().count()

	total_ordered_agreements_guest_users_count = get_ordered_agreements_guest_users_count()
	total_users_count['total_ordered_agreements_guest_users_count'] = total_ordered_agreements_guest_users_count
	
	total_ordered_services_guest_users_count = get_ordered_services_guest_users_count()
	total_users_count['total_ordered_services_guest_users_count'] = total_ordered_services_guest_users_count
	
	total_ordered_getQuote_services_guest_users_count = get_ordered_getQuote_services_guest_users_count()
	total_users_count['total_ordered_getQuote_services_guest_users_count'] = total_ordered_getQuote_services_guest_users_count
	
	total_guest_users_count = total_ordered_agreements_guest_users_count + total_ordered_services_guest_users_count + total_ordered_getQuote_services_guest_users_count
	total_users_count['total_guest_users_count'] = total_guest_users_count
	total_users_count['total_count'] = total_guest_users_count + total_users_count['total_signedup_users_count']
	context['total_users_count'] = total_users_count

	# Total Earning amount for now
	context['total_earning_amount'] = orders_counts['total_amount'] + total_service_orders_data['total_amount'] + total_service_get_quote_orders_data['total_amount']
	
	return render(request, 'master_admin/index.html', context=context)

# def contact_us(request):    # added
# 	"""
# 		Contact us page
# 	"""
# 	return render(request,'contact_us.html')


def cancellation_and_refund_policy(request):    # added
	"""
		Contact us page
	"""
	return render(request,'cancellation_and_refund_policy.html')


def admin_dashboard_make_with_ai_user_chats(request):
	chat_objects = Chat.objects.all().order_by('-timestamp')
	context = {
		"chats" : chat_objects
	}
	return render(request,"master_admin/chats.html", context)


def admin_dashboard_make_with_ai_user_chat_detail(request, chat_id):
	chat_obj = Chat.objects.filter(id=chat_id).first()
	context = {
		"chat_obj" : chat_obj
	}
	return render(request,"master_admin/chat.html", context)



def admin_dashboard_change_agreement_price(request, agreement_type):
	if request.method == 'GET':
		return render(request, "master_admin/change_price.html")

	elif request.method == 'POST':
		price_value = int(request.POST.get('price'))
		all_agreements = Aggrement.objects.all()
		
		if agreement_type == "word":
			msg = "Word price changed."
			for obj in all_agreements:
				obj.word_price = price_value
				obj.save()
				
		elif agreement_type == "pdf":
			msg = "PDF price changed."
			for obj in all_agreements:
				obj.pdf_price = price_value
				obj.save()
				
		elif agreement_type == "make_with_ai":
			msg = "Make With AI price changed."
			for obj in all_agreements:
				obj.make_with_ai_price = price_value
				obj.save()
				
		elif agreement_type == "make_with_legal_help":
			msg = "Make With Legal Help price changed."
			for obj in all_agreements:
				obj.make_with_legal_help_price = price_value
				obj.save()

		return render(request, "master_admin/change_price.html", {"type": agreement_type, "message": msg})


def admin_dashboard_change_email_sequence_days_to_send_pending_orders_email(request):
	if request.method == 'GET':
		return render(request, "master_admin/change_email_sequence_days.html")
	else:
		days = int(request.POST.get('days'))
		objects = WelcomeMail.objects.all()
		objects.update(days=days)
		msg = "Email sequence days changed."
		return render(request, "master_admin/change_email_sequence_days.html", {"message": msg})


def admin_dashboard_orders(request, order_type):
	context = {}
	if order_type == 'agreement':
		# Handle agreement orders
		order_type = "agreement"
		completed_orders = OrderAgreement.objects.filter(
			order_status="COMPLETED" #payment_status="PAYMENT_SUCCESS"
		).order_by("-updated_at")
		pending_orders = OrderAgreement.objects.filter(
			order_status="PENDING" # Q(payment_status="PAYMENT_INITIATED") | Q(payment_status="PAYMENT_ERROR")
		).order_by("-updated_at")
		failed_orders = OrderAgreement.objects.filter(
			payment_status="PAYMENT_ERROR"
		).order_by("-updated_at")
		all_orders = OrderAgreement.objects.all().order_by("-updated_at")

		context['completed_orders'] = completed_orders
		context['pending_orders'] = pending_orders
		context['failed_orders'] = failed_orders
		context['all_orders'] = all_orders

	elif order_type == 'repeated-user-agreement':
		order_type == 'repeated-user-agreement'
		repeated_user_agreement_orders = repeated_agreement_orders_by_user()
		context['completed_orders'] = repeated_user_agreement_orders['completed_orders']
		context['pending_orders'] = repeated_user_agreement_orders['pending_orders']
		context['failed_orders'] = repeated_user_agreement_orders['failed_orders']
		context['all_orders'] = repeated_user_agreement_orders['all_orders']
		

	elif order_type == 'repeated-user-service':
		order_type == 'repeated-user-service'
		repeated_user_service_orders = repeated_service_orders_by_user()
		context['orders'] = repeated_user_service_orders['repeated_orders']


	elif order_type == 'service':
		# Handle service orders
		order_type = "service"
		completed_orders = OrderService.objects.filter(
			order_status="COMPLETED" # payment_status="captured"
		).order_by("-updated_at")
		pending_orders = OrderService.objects.filter(
			order_status = "PENDING" # Q(payment_status="created") | Q(payment_status="failed")
		).order_by("-updated_at")
		failed_orders = OrderService.objects.filter(
			payment_status="failed"
		).order_by("-updated_at")
		all_orders = OrderService.objects.all().order_by("-updated_at")
		
		list_objects = []
		for order in all_orders:
			if not order.service:
				list_objects.append(order.id)
				
		context['completed_orders'] = completed_orders
		context['pending_orders'] = pending_orders
		context['failed_orders'] = failed_orders
		context['all_orders'] = all_orders

	elif order_type == 'service-get-quote':
		# Handle service-get-a-quote orders
		order_type = "service-get-quote"
		orders = GetQuoteService.objects.all().order_by("-created_at")
		context['orders'] = orders
	else:
		# Handle the case where the path does not match expected patterns
		return CommonMixin.render(request, '404.html' ,context)
		
	context['order_type'] = order_type
	return render(request,"master_admin/orders.html", context)


def admin_dashboard_order_detail(request, order_type, order_id):
	if order_type == 'agreement':
		order = OrderAgreement.objects.filter(id=order_id).first()
	elif order_type == 'service':
		order = OrderService.objects.filter(id=order_id).first()
	elif order_type == 'service-get-quote':
		order = GetQuoteService.objects.filter(id=order_id).first()
	else:
		# Handle the case where the path does not match expected patterns
		return CommonMixin.render(request, '404.html' ,context)
	context = {}
	context['order'] = order
	context['order_type'] = order_type
	return render(request,"master_admin/order_detail.html", context)


def admin_dashboard_agreement_detail(request, agreement_id):
	agreement = Aggrement.objects.filter(id=agreement_id).first()
	context = {'agreement': agreement}
	return render(request,"master_admin/agreement_detail.html", context)


def admin_dashboard_service_detail(request, service_id):
	service = Services.objects.filter(id=service_id).first()
	context = {'service': service}
	return render(request,"master_admin/service_detail.html", context)



class ServiceListViewAdmin(LoginRequiredMixin, ListView):
	"""
	Service List View
	"""
	model = Aggrement
	context_object_name = 'service_list'
	template_name = 'master_admin/service_list.html'
	paginate_by = 15

	def get(self, request, *args, **kwargs):
	# checking if the user is customer
		try:
			if not self.request.user.is_superuser:
				messages.error(self.request, 'You have no permission to access the requested resource!')
				return redirect(reverse('index'))
		except AttributeError as error:
			messages.error(self.request, 'You have no permission to access the requested resource!')
			return redirect(reverse('index'))

		return super().get(request, *args, **kwargs)

	def get_queryset(self):
		service_list = Services.objects.all().order_by('id')
		return service_list



def autocomplete(request):
	matched_data = list()
	qs = None
	term = request.GET.get('term')
	
	if not term:
		return JsonResponse({'error': 'No search term provided'}, status=400)
	
	# Check for ID matches
	try:
		term_id = int(term)
		
		agreement_objects_by_id = Aggrement.objects.filter(id=term_id)
		if agreement_objects_by_id.exists():
			qs = agreement_objects_by_id
			d = {}
			d['model_name'] = 'Aggrement'
			d['queryset'] = [aggrement.to_dict() for aggrement in qs] #list(qs.values('id'))
			d['matched_with'] = 'id'
			matched_data.append(d)
	

		service_objects_by_id = Services.objects.filter(id=term_id)
		if service_objects_by_id.exists():
			qs = service_objects_by_id
			d = {}
			d['model_name'] = 'Services'
			d['queryset'] = [service.to_dict() for service in qs] #list(qs.values('id'))
			d['matched_with'] = 'id'
			matched_data.append(d)
	

		get_quote_service_objects_by_id = GetQuoteService.objects.filter(id=term_id)
		if get_quote_service_objects_by_id.exists():
			qs = get_quote_service_objects_by_id
			# for obj in qs:
			# 	# matched_ids.append({'model_name': 'get_quote_service', 'model_field_name': 'id'})
			# 	# Convert the model instance to a dictionary of its field values
			# 	obj_dict = {field.name: getattr(obj, field.name) for field in obj._meta.fields}
				
			# 	# Add the additional fields to the dictionary
			# 	obj_dict['model_name'] = 'get_quote_service'
			# 	obj_dict['model_field_name'] = 'id'
				
			# 	# Append the dictionary to the results list
			# 	matched_ids.append(obj_dict)
			d = {}
			d['model_name'] = 'GetQuoteService'
			d['queryset'] = list(qs.values('id'))
			d['matched_with'] = 'id'
			matched_data.append(d)
	

		order_agreement_objects_by_id = OrderAgreement.objects.filter(id=term_id)
		if order_agreement_objects_by_id.exists():
			qs = order_agreement_objects_by_id
			d = {}
			d['model_name'] = 'OrderAgreement'
			d['queryset'] = list(qs.values('id'))
			d['matched_with'] = 'id'
			matched_data.append(d)
	

		order_service_objects_by_id = OrderService.objects.filter(id=term_id)
		if order_service_objects_by_id.exists():
			qs = order_service_objects_by_id
			d = {}
			d['model_name'] = 'OrderService'
			d['queryset'] = list(qs.values('id'))
			d['matched_with'] = 'id'
			matched_data.append(d)
	

		agreement_objects_by_category_id = Aggrement.objects.filter(aggrement_category__id=term_id).distinct()[:10]
		if agreement_objects_by_category_id.exists():
			qs = agreement_objects_by_category_id
			d = {}
			d['model_name'] = 'Aggrement'
			d['queryset'] = [agreement.to_dict() for agreement in qs] #list(qs.values('id'))
			d['matched_with'] = 'aggrement_category__id'
			matched_data.append(d)


		service_objects_by_category_id = Services.objects.filter(category__id=term_id).distinct()[:10]
		if service_objects_by_category_id.exists():
			qs = service_objects_by_category_id
			d = {}
			d['model_name'] = 'Services'
			d['queryset'] = [service.to_dict() for service in qs] #list(qs.values('id'))
			d['matched_with'] = 'category__id'
			matched_data.append(d)
	

		service_objects_by_subcategory_id = Services.objects.filter(sub_category__id=term_id).distinct()[:10]
		if service_objects_by_subcategory_id.exists():
			qs = service_objects_by_subcategory_id
			d = {}
			d['model_name'] = 'Services'
			d['queryset'] = [service.to_dict() for service in qs] # list(qs.values('id'))
			d['matched_with'] = 'sub_category__id'
			matched_data.append(d)
	

		order_agreement_objects_by_agreement_id = OrderAgreement.objects.filter(agreement__id=term_id).distinct()[:10]
		if order_agreement_objects_by_agreement_id.exists():
			qs = order_agreement_objects_by_agreement_id
			d = {}
			d['model_name'] = 'OrderAgreement'
			d['queryset'] = list(qs.values('id'))
			d['matched_with'] = 'agreement__id'
			matched_data.append(d)
	

		order_service_objects_by_service_id = OrderService.objects.filter(service__id=term_id).distinct()[:10]
		if order_service_objects_by_service_id.exists():
			qs = order_service_objects_by_service_id
			d = {}
			d['model_name'] = 'OrderService'
			d['queryset'] = list(qs.values('id'))
			d['matched_with'] = 'service__id'
			matched_data.append(d)
	

		order_service_objects_by_service_category_id = OrderService.objects.filter(service__category__id=term_id).distinct()[:10]
		if order_service_objects_by_service_category_id.exists():
			qs = order_service_objects_by_service_category_id
			d = {}
			d['model_name'] = 'OrderService'
			d['queryset'] = list(qs.values('id'))
			d['matched_with'] = 'service__category__id'
			matched_data.append(d)
	

		order_service_objects_by_service_sub_category_id = OrderService.objects.filter(service__sub_category__id=term_id).distinct()[:10]
		if order_service_objects_by_service_sub_category_id.exists():
			qs = order_service_objects_by_service_sub_category_id
			d = {}
			d['model_name'] = 'OrderService'
			d['queryset'] = list(qs.values('id'))
			d['matched_with'] = 'service__sub_category__id'
			matched_data.append(d)


		get_quote_service_objects_by_service_id = GetQuoteService.objects.filter(service__id=term_id).distinct()[:10]
		if get_quote_service_objects_by_service_id.exists():
			qs = get_quote_service_objects_by_service_id
			d = {}
			d['model_name'] = 'GetQuoteService'
			d['queryset'] = list(qs.values('id'))
			d['matched_with'] = 'service__id'
			matched_data.append(d)
	
		
		order_agreement_objects_by_agreement_category_id = OrderAgreement.objects.filter(agreement__aggrement_category__id=term_id).distinct()[:10]
		if order_agreement_objects_by_agreement_category_id.exists():
			qs = order_agreement_objects_by_agreement_category_id
			d = {}
			d['model_name'] = 'OrderAgreement'
			d['queryset'] = list(qs.values('id'))
			d['matched_with'] = 'agreement__aggrement_category__id'
			matched_data.append(d)

		order_agreement_objects_by_user_id = OrderAgreement.objects.filter(user__id=term_id).distinct()[:10]
		if order_agreement_objects_by_user_id.exists():
			qs = order_agreement_objects_by_user_id
			d = {}
			d['model_name'] = 'OrderAgreement'
			d['queryset'] = list(qs.values('id'))
			d['matched_with'] = 'user__id'
			matched_data.append(d)
	

		order_service_objects_by_user_id = OrderService.objects.filter(user__id=term_id).distinct()[:10]
		if order_service_objects_by_user_id.exists():
			qs = order_service_objects_by_user_id
			d = {}
			d['model_name'] = 'OrderService'
			d['queryset'] = list(qs.values('id'))
			d['matched_with'] = 'user__id'
			matched_data.append(d)
	

		get_quote_service_objects_by_user_id = GetQuoteService.objects.filter(user__id=term_id).distinct()[:10]
		if get_quote_service_objects_by_user_id.exists():
			qs = get_quote_service_objects_by_user_id
			d = {}
			d['model_name'] = 'GetQuoteService'
			d['queryset'] = list(qs.values('id'))
			d['matched_with'] = 'user__id'
			matched_data.append(d)
		
			
	except ValueError:
		term_id = None
	
	
	#=================
	# t = term.strip()
	# print("====== t : ",t)
	# if Aggrement.objects.filter(title__istartswith=t).exists():
	# 	print("======== startswith : ")
	# 	qs = Aggrement.objects.filter(title__istartswith=t).distinct()[:10]
	# 	print("======== queryset startswith : ",qs)
		
	# 	d = {}
	# 	d['model_name'] = 'Aggrement'
	# 	d['queryset'] = list(qs.values('id'))
	# 	matched_data.append(d)


	service_objects_by_title = Services.objects.filter(service_title__icontains=term).distinct()[:10]
	if service_objects_by_title.exists():
		qs = service_objects_by_title
		d = {}
		d['model_name'] = 'Services'
		d['queryset'] = [service.to_dict() for service in qs]
		d['matched_with'] = 'service_title'
		matched_data.append(d)

	
	get_quote_service_objects_by_title = GetQuoteService.objects.filter(service__service_title__icontains=term).distinct()[:10]
	if get_quote_service_objects_by_title.exists():
		qs = get_quote_service_objects_by_title
		d = {}
		d['model_name'] = 'GetQuoteService'
		d['queryset'] = list(qs.values('id'))
		d['matched_with'] = 'service__service_title'
		matched_data.append(d)

	agreement_objects_by_category_name = Aggrement.objects.filter(aggrement_category__name__icontains=term).distinct()[:10]
	if agreement_objects_by_category_name.exists():
		qs = agreement_objects_by_category_name
		d = {}
		d['model_name'] = 'Aggrement'
		d['queryset'] = [aggrement.to_dict() for aggrement in qs]
		d['matched_with'] = 'aggrement_category__name'
		matched_data.append(d)

	
	service_objects_by_category_name = Services.objects.filter(category__name__icontains=term).distinct()[:10]
	if service_objects_by_category_name.exists():
		qs = service_objects_by_category_name
		d = {}
		d['model_name'] = 'Services'
		d['queryset'] = [service.to_dict() for service in qs]
		d['matched_with'] = 'category__name'
		matched_data.append(d)
	

	service_objects_by_subcategory_name = Services.objects.filter(sub_category__name__icontains=term).distinct()[:10]
	if service_objects_by_subcategory_name.exists():
		qs = service_objects_by_subcategory_name 
		d = {}
		d['model_name'] = 'Services'
		d['queryset'] = [service.to_dict() for service in qs]
		d['matched_with'] = 'sub_category__name'
		matched_data.append(d)

	
	order_agreement_objects_by_title= OrderAgreement.objects.filter(agreement__title__icontains=term).distinct()[:10]
	if order_agreement_objects_by_title.exists():
		qs = order_agreement_objects_by_title
		d = {}
		d['model_name'] = 'OrderAgreement'
		d['queryset'] = list(qs.values('id'))
		d['matched_with'] = 'agreement__title'
		matched_data.append(d)


	order_agreement_objects_by_category_name = OrderAgreement.objects.filter(agreement__aggrement_category__name__icontains=term).distinct()[:10]
	if order_agreement_objects_by_category_name.exists():
		qs = order_agreement_objects_by_category_name
		d = {}
		d['model_name'] = 'OrderAgreement'
		d['queryset'] = list(qs.values('id'))
		d['matched_with'] = 'agreement__aggrement_category__name'
		matched_data.append(d)
	

	order_service_objects_by_category_name = OrderService.objects.filter(service__category__name__icontains=term).distinct()[:10]
	if order_service_objects_by_category_name.exists(): 
		qs = order_service_objects_by_category_name
		d = {}
		d['model_name'] = 'OrderService'
		d['queryset'] = list(qs.values('id'))
		d['matched_with'] = 'service__category__name'
		matched_data.append(d)
	

	order_service_objects_by_sub_category_name = OrderService.objects.filter(service__sub_category__name__icontains=term).distinct()[:10]
	if order_service_objects_by_sub_category_name.exists():
		qs = order_service_objects_by_sub_category_name
		d = {}
		d['model_name'] = 'OrderService'
		d['queryset'] = list(qs.values('id'))
		d['matched_with'] = 'service__sub_category__name'
		matched_data.append(d)


	agreement_objects_by_title = Aggrement.objects.filter(title__icontains=term).distinct()[:10]
	if agreement_objects_by_title.exists():
		qs = agreement_objects_by_title
		d = {}
		d['model_name'] = 'Aggrement'
		d['queryset'] = [aggrement.to_dict() for aggrement in qs]
		d['matched_with'] = 'title'
		matched_data.append(d)


	# Initialize the total number of objects in a queryset to keep
	remaining_queryset = 10
	result_data = []

	# Iterate through each model
	for model in matched_data:
		if remaining_queryset > 0:
			# Calculate the number of queryset_objects to take from the current model
			num_queryset_objects_to_take = min(len(model['queryset']), remaining_queryset)
			# Take the required number of queryset_objects from the current model
			taken_queryset_objects = model['queryset'][:num_queryset_objects_to_take]
			# Add the model with the taken queryset_objects to the result data
			result_data.append({
				'model_name': model['model_name'],
				'queryset': taken_queryset_objects,
				'matched_with': model['matched_with']
			})
			# Decrease the remaining queryset_objects count
			remaining_queryset -= num_queryset_objects_to_take
		else:
			break
	print("=========== result data : ",result_data)
	response_data = {
		'matched_data': result_data
	}
	return JsonResponse(response_data, safe=False)



def can_add(data):
	length = len(data)
	if length >= 10:
		return False
	else:
		return True



def get_search_suggestions(term):
	qs = None
	matched_data = list()
	# Check for ID matches
	try:
		term_id = int(term)
		
		agreement_objects_by_id = Aggrement.objects.filter(id=term_id)
		if agreement_objects_by_id.exists() and can_add(matched_data):
			qs = agreement_objects_by_id

			# ======
			for obj in qs:
				d = {}
				d['model_name'] = 'Aggrement'
				d['obj'] = obj.to_dict()
				d['matched_with'] = 'id'
				d['link_text'] = 'agreement In Agreements'
				d['link_text1'] = 'agreement with ID: '
				d['link_text2'] = 'In Agreements'
				if can_add(matched_data):
					matched_data.append(d)
			#=======
			# d = {}
			# d['model_name'] = 'Aggrement'
			# d['queryset'] = [aggrement.to_dict() for aggrement in qs] #list(qs.values('id'))
			# d['matched_with'] = 'id'
			# matched_data.append(d)
	
		
		service_objects_by_id = Services.objects.filter(id=term_id)
		if service_objects_by_id.exists() and can_add(matched_data):
			qs = service_objects_by_id
			# d = {}
			# d['model_name'] = 'Services'
			# d['queryset'] = [service.to_dict() for service in qs] #list(qs.values('id'))
			# d['matched_with'] = 'id'
			# matched_data.append(d)

			# ======
			for obj in qs:
				d = {}
				d['model_name'] = 'Services'
				d['obj'] = obj.to_dict() #list(qs.values('id'))
				d['matched_with'] = 'id'
				d['link_text'] = 'service In Services'
				d['link_text1'] = 'service with ID: '
				d['link_text2'] = 'In Services'
				if can_add(matched_data):
					matched_data.append(d)
	

		get_quote_service_objects_by_id = GetQuoteService.objects.filter(id=term_id)
		if get_quote_service_objects_by_id.exists() and can_add(matched_data):
			qs = get_quote_service_objects_by_id
			
			# d = {}
			# d['model_name'] = 'GetQuoteService'
			# d['queryset'] = list(qs.values('id'))
			# d['matched_with'] = 'id'
			# matched_data.append(d)
   
			# ======
			for obj in qs:
				d = {}
				d['model_name'] = 'GetQuoteService'
				d['obj'] = obj.id
				d['matched_with'] = 'id'
				d['link_text'] = 'get-quote-service In Get-Quote-Services'
				d['link_text1'] = 'get-quote-service with ID: '
				d['link_text2'] = 'In Get-Quote-Services'
				if can_add(matched_data):
					matched_data.append(d)
	

		order_agreement_objects_by_id = OrderAgreement.objects.filter(id=term_id)
		if order_agreement_objects_by_id.exists() and can_add(matched_data):
			qs = order_agreement_objects_by_id
			# d = {}
			# d['model_name'] = 'OrderAgreement'
			# d['obj'] = list(qs.values('id'))
			# d['matched_with'] = 'id'
			# matched_data.append(d)

			# ======
			for obj in qs:
				d = {}
				d['model_name'] = 'OrderAgreement'
				d['obj'] = obj.id
				d['matched_with'] = 'id'
				d['link_text'] = 'order In Agreement Orders'
				d['link_text1'] = 'order with ID: '
				d['link_text2'] = 'In Agreement Orders'
				if can_add(matched_data):
					matched_data.append(d)

		order_service_objects_by_id = OrderService.objects.filter(id=term_id)
		if order_service_objects_by_id.exists() and can_add(matched_data):
			qs = order_service_objects_by_id
			# d = {}
			# d['model_name'] = 'OrderService'
			# d['queryset'] = list(qs.values('id'))
			# d['matched_with'] = 'id'
			# matched_data.append(d)
   
			# ======
			for obj in qs:
				d = {}
				d['model_name'] = 'OrderService'
				d['obj'] = obj.id
				d['matched_with'] = 'id'
				d['link_text'] = 'order In Service Orders'
				d['link_text1'] = 'order with ID: '
				d['link_text2'] = 'In Service Orders'
				if can_add(matched_data):
					matched_data.append(d)


		agreement_objects_by_category_id = Aggrement.objects.filter(aggrement_category__id=term_id).distinct()[:10]
		if agreement_objects_by_category_id.exists() and can_add(matched_data):
			qs = agreement_objects_by_category_id
			# d = {}
			# d['model_name'] = 'Aggrement'
			# d['queryset'] = [agreement.to_dict() for agreement in qs] #list(qs.values('id'))
			# d['matched_with'] = 'aggrement_category__id'
			# matched_data.append(d)
	
			# =====
			for obj in qs:
				d = {}
				d['model_name'] = 'Aggrement'
				d['obj'] = obj.to_dict() #list(qs.values('id'))
				d['matched_with'] = 'aggrement_category__id'
				d['link_text'] = 'agreement-category In Agreement'
				d['link_text1'] = 'agreement with category-ID: '
				d['link_text2'] = 'In Agreements'
				if can_add(matched_data):
					matched_data.append(d)


		service_objects_by_category_id = Services.objects.filter(category__id=term_id).distinct()[:10]
		if service_objects_by_category_id.exists() and can_add(matched_data):
			qs = service_objects_by_category_id
			d = {}
			# d['model_name'] = 'Services'
			# d['queryset'] = [service.to_dict() for service in qs] #list(qs.values('id'))
			# d['matched_with'] = 'category__id'
			# matched_data.append(d)
   
			#=====
			for obj in qs:
				d = {}
				d['model_name'] = 'Services'
				d['obj'] = obj.to_dict() #list(qs.values('id'))
				d['matched_with'] = 'category__id'
				d['link_text'] = 'category In Service'
				d['link_text1'] = 'service with category-ID: '
				d['link_text2'] = 'In Services'
				if can_add(matched_data):
					matched_data.append(d)
	

		service_objects_by_subcategory_id = Services.objects.filter(sub_category__id=term_id).distinct()[:10]
		if service_objects_by_subcategory_id.exists() and can_add(matched_data):
			qs = service_objects_by_subcategory_id
			# d = {}
			# d['model_name'] = 'Services'
			# d['queryset'] = [service.to_dict() for service in qs] # list(qs.values('id'))
			# d['matched_with'] = 'sub_category__id'
			# matched_data.append(d)

			# ======
			for obj in qs:
				d = {}
				d['model_name'] = 'Services'
				d['obj'] = obj.to_dict() # list(qs.values('id'))
				d['matched_with'] = 'sub_category__id'
				d['link_text'] = 'subcategory In Service'
				d['link_text1'] = 'service with subcategory-ID: '
				d['link_text2'] = 'In Services'
				if can_add(matched_data):
					matched_data.append(d)
	

		order_agreement_objects_by_agreement_id = OrderAgreement.objects.filter(agreement__id=term_id).distinct()[:10]
		if order_agreement_objects_by_agreement_id.exists() and can_add(matched_data):
			qs = order_agreement_objects_by_agreement_id
			# d = {}
			# d['model_name'] = 'OrderAgreement'
			# d['queryset'] = list(qs.values('id'))
			# d['matched_with'] = 'agreement__id'
			# matched_data.append(d)
   
			# =========
			for obj in qs:
				d = {}
				d['model_name'] = 'OrderAgreement'
				d['obj'] = obj.id
				d['matched_with'] = 'agreement__id'
				d['link_text'] = 'agreement In Agreement Order'
				d['link_text1'] = 'order with agreement-ID: '
				d['link_text2'] = 'In Agreement Orders'
				if can_add(matched_data):
					matched_data.append(d)
	   
	

		order_service_objects_by_service_id = OrderService.objects.filter(service__id=term_id).distinct()[:10]
		if order_service_objects_by_service_id.exists() and can_add(matched_data):
			qs = order_service_objects_by_service_id
			# d = {}
			# d['model_name'] = 'OrderService'
			# d['queryset'] = list(qs.values('id'))
			# d['matched_with'] = 'service__id'
			# matched_data.append(d)
	
			# ===
			for obj in qs:
				d = {}
				d['model_name'] = 'OrderService'
				d['obj'] = obj.id
				d['matched_with'] = 'service__id'
				d['link_text'] = 'service In Service Order'
				d['link_text1'] = 'order with service-ID: '
				d['link_text2'] = 'In Service Orders'
				if can_add(matched_data):
					matched_data.append(d)
	

		order_service_objects_by_service_category_id = OrderService.objects.filter(service__category__id=term_id).distinct()[:10]
		if order_service_objects_by_service_category_id.exists() and can_add(matched_data):
			qs = order_service_objects_by_service_category_id
			# d = {}
			# d['model_name'] = 'OrderService'
			# d['queryset'] = list(qs.values('id'))
			# d['matched_with'] = 'service__category__id'
			# matched_data.append(d)
	
			#=====
			for obj in qs:
				d = {}
				d['model_name'] = 'OrderService'
				d['obj'] = obj.id
				d['matched_with'] = 'service__category__id'
				d['link_text'] = 'service-category In Service Order'
				d['link_text1'] = 'order with service-category-ID: '
				d['link_text2'] = 'In Service Orders'
				if can_add(matched_data):
					matched_data.append(d)

		order_service_objects_by_service_sub_category_id = OrderService.objects.filter(service__sub_category__id=term_id).distinct()[:10]
		if order_service_objects_by_service_sub_category_id.exists() and can_add(matched_data):
			qs = order_service_objects_by_service_sub_category_id
			# d = {}
			# d['model_name'] = 'OrderService'
			# d['queryset'] = list(qs.values('id'))
			# d['matched_with'] = 'service__sub_category__id'
			# matched_data.append(d)
   
			# =====
			for obj in qs:
				d = {}
				d['model_name'] = 'OrderService'
				d['obj'] = obj.id
				d['matched_with'] = 'service__sub_category__id'
				d['link_text'] = 'service-subcategory In Service Order'
				d['link_text1'] = 'order with service-subcategory-ID: '
				d['link_text2'] = 'In Service Orders'
				if can_add(matched_data):
					matched_data.append(d)


		get_quote_service_objects_by_service_id = GetQuoteService.objects.filter(service__id=term_id).distinct()[:10]
		if get_quote_service_objects_by_service_id.exists() and can_add(matched_data):
			qs = get_quote_service_objects_by_service_id
			# d = {}
			# d['model_name'] = 'GetQuoteService'
			# d['queryset'] = list(qs.values('id'))
			# d['matched_with'] = 'service__id'
			# matched_data.append(d)

			# =====
			for obj in qs:
				d = {}
				d['model_name'] = 'GetQuoteService'
				d['obj'] = obj.id
				d['matched_with'] = 'service__id'
				d['link_text'] = 'service In Get-Quote-Services'
				d['link_text1'] = 'get-quote-service with service-ID: '
				d['link_text2'] = 'In Service Orders'
				if can_add(matched_data):
					matched_data.append(d)
	
		
		order_agreement_objects_by_agreement_category_id = OrderAgreement.objects.filter(agreement__aggrement_category__id=term_id).distinct()[:10]
		if order_agreement_objects_by_agreement_category_id.exists() and can_add(matched_data):
			qs = order_agreement_objects_by_agreement_category_id
			# d = {}
			# d['model_name'] = 'OrderAgreement'
			# d['queryset'] = list(qs.values('id'))
			# d['matched_with'] = 'agreement__aggrement_category__id'
			# matched_data.append(d)

			# =====
			for obj in qs:
				d = {}
				d['model_name'] = 'OrderAgreement'
				d['obj'] = obj.id
				d['matched_with'] = 'agreement__aggrement_category__id'
				d['link_text'] = 'agreement-category In Agreement Order'
				d['link_text1'] = "order with agreement-category-ID: "
				d['link_text2'] = 'In Agreement Orders'
				if can_add(matched_data):
					matched_data.append(d)

		order_agreement_objects_by_user_id = OrderAgreement.objects.filter(user__id=term_id).distinct()[:10]
		if order_agreement_objects_by_user_id.exists() and can_add(matched_data):
			qs = order_agreement_objects_by_user_id
			# d = {}
			# d['model_name'] = 'OrderAgreement'
			# d['queryset'] = list(qs.values('id'))
			# d['matched_with'] = 'user__id'
			# matched_data.append(d)

			# =====
			for obj in qs:
				d = {}
				d['model_name'] = 'OrderAgreement'
				d['obj'] = obj.id
				d['matched_with'] = 'user__id'
				d['link_text'] = 'user In Agreement Order'
				d['link_text1'] = 'order with user-ID: '
				d['link_text2'] = 'In Agreement Orders'
				if can_add(matched_data):
					matched_data.append(d)
	

		order_service_objects_by_user_id = OrderService.objects.filter(user__id=term_id).distinct()[:10]
		if order_service_objects_by_user_id.exists() and can_add(matched_data):
			qs = order_service_objects_by_user_id
			# d = {}
			# d['model_name'] = 'OrderService'
			# d['queryset'] = list(qs.values('id'))
			# d['matched_with'] = 'user__id'
			# matched_data.append(d)
   
			#===
			for obj in qs:
				d = {}
				d['model_name'] = 'OrderService'
				d['obj'] = obj.id
				d['matched_with'] = 'user__id'
				d['link_text'] = 'user In Service Order'
				d['link_text1'] = 'order with user-ID: '
				d['link_text2'] = 'In Service Orders'
				if can_add(matched_data):
					matched_data.append(d)
	

		get_quote_service_objects_by_user_id = GetQuoteService.objects.filter(user__id=term_id).distinct()[:10]
		if get_quote_service_objects_by_user_id.exists() and can_add(matched_data):
			qs = get_quote_service_objects_by_user_id
			# d = {}
			# d['model_name'] = 'GetQuoteService'
			# d['queryset'] = list(qs.values('id'))
			# d['matched_with'] = 'user__id'
			# matched_data.append(d)

			#===
			for obj in qs:
				d = {}
				d['model_name'] = 'GetQuoteService'
				d['obj'] = obj.id
				d['matched_with'] = 'user__id'
				d['link_text'] = 'user In Get-Quote-Service'
				d['link_text1'] = 'get-quote-service with user-ID: '
				d['link_text2'] = 'In GetQuoteServices'
				if can_add(matched_data):
					matched_data.append(d)
		
			
	except ValueError:
		term_id = None
	

	# import urllib.parse

	# term = urllib.parse.unquote(term)
	# print("========== term changes : ",term)
	# order_agreement_objects_by_user_email = OrderAgreement.objects.filter(user__email__icontains=term).distinct()[:10]
	# print("============ OrderAgreement user-email====== : ",order_agreement_objects_by_user_email)
	# if order_agreement_objects_by_user_email.exists() and can_add(matched_data):
	# 	qs = order_agreement_objects_by_user_email
	# 	# d = {}
	# 	# d['model_name'] = 'OrderAgreement'
	# 	# d['queryset'] = list(qs.values('id'))
	# 	# d['matched_with'] = 'user__id'
	# 	# matched_data.append(d)

	# 	# =====
	# 	for obj in qs:
	# 		d = {}
	# 		d['model_name'] = 'OrderAgreement'
	# 		d['obj'] = obj.id
	# 		d['matched_with'] = 'user__email'
	# 		d['link_text'] = 'user In Agreement Order'
	# 		d['link_text1'] = 'order with user-email: '
	# 		d['link_text2'] = 'In Agreement Orders'
	# 		if can_add(matched_data):
	# 			matched_data.append(d)
	
	

	service_objects_by_title = Services.objects.filter(service_title__icontains=term).distinct()[:10]
	if service_objects_by_title.exists() and can_add(matched_data):
		qs = service_objects_by_title
		# d = {}
		# d['model_name'] = 'Services'
		# d['queryset'] = [service.to_dict() for service in qs]
		# d['matched_with'] = 'service_title'
		# matched_data.append(d)

		for obj in qs:
			d = {}
			d['model_name'] = 'Services'
			d['obj'] = obj.to_dict()
			d['matched_with'] = 'service_title'
			d['link_text'] = 'service In Services'
			d['link_text1'] = 'service with title: '
			d['link_text2'] = 'In Services'
			if can_add(matched_data):
				matched_data.append(d)

	
	get_quote_service_objects_by_title = GetQuoteService.objects.filter(service__service_title__icontains=term).distinct()[:10]
	if get_quote_service_objects_by_title.exists() and can_add(matched_data):
		qs = get_quote_service_objects_by_title
		# d = {}
		# d['model_name'] = 'GetQuoteService'
		# d['queryset'] = list(qs.values('id'))
		# d['matched_with'] = 'service__service_title'
		# matched_data.append(d)
		for obj in qs:
			d = {}
			d['model_name'] = 'GetQuoteService'
			d['obj'] = obj.id
			d['matched_with'] = 'service__service_title'
			d['link_text'] = 'service In GetQuoteServices'
			d['link_text1'] = 'get-quote-service with service-title: '
			d['link_text2'] = 'In GetQuoteServices'
			if can_add(matched_data):
				matched_data.append(d)

	agreement_objects_by_category_name = Aggrement.objects.filter(aggrement_category__name__icontains=term).distinct()[:10]
	if agreement_objects_by_category_name.exists() and can_add(matched_data):
		qs = agreement_objects_by_category_name
		# d = {}
		# d['model_name'] = 'Aggrement'
		# d['queryset'] = [aggrement.to_dict() for aggrement in qs]
		# d['matched_with'] = 'aggrement_category__name'
		# matched_data.append(d)
		for obj in qs:
			d = {}
			d['model_name'] = 'Aggrement'
			d['obj'] = obj.to_dict()
			d['matched_with'] = 'aggrement_category__name'
			d['link_text'] = 'agreement-category In Agreement'
			d['link_text1'] = 'agreement with category-name: '
			d['link_text2'] = 'In Agreements'
			if can_add(matched_data):
				matched_data.append(d)

	
	service_objects_by_category_name = Services.objects.filter(category__name__icontains=term).distinct()[:10]
	if service_objects_by_category_name.exists() and can_add(matched_data):
		qs = service_objects_by_category_name
		# d = {}
		# d['model_name'] = 'Services'
		# d['queryset'] = [service.to_dict() for service in qs]
		# d['matched_with'] = 'category__name'
		# matched_data.append(d)

		for obj in qs:
			d = {}
			d['model_name'] = 'Services'
			d['obj'] = obj.to_dict()
			d['matched_with'] = 'category__name'
			d['link_text'] = 'category In Service'
			d['link_text1'] = 'service with category-name: '
			d['link_text2'] = 'In Services'
			if can_add(matched_data):
				matched_data.append(d)
	

	service_objects_by_subcategory_name = Services.objects.filter(sub_category__name__icontains=term).distinct()[:10]
	if service_objects_by_subcategory_name.exists() and can_add(matched_data):
		qs = service_objects_by_subcategory_name 
		# d = {}
		# d['model_name'] = 'Services'
		# d['queryset'] = [service.to_dict() for service in qs]
		# d['matched_with'] = 'sub_category__name'
		# matched_data.append(d)

		for obj in qs:
			d = {}
			d['model_name'] = 'Services'
			d['obj'] = obj.to_dict()
			d['matched_with'] = 'sub_category__name'
			d['link_text'] = 'subcategory In Service'
			d['link_text1'] = 'service with subcategory-name: '
			d['link_text2'] = 'In Services'
			if can_add(matched_data):
				matched_data.append(d)

	
	order_agreement_objects_by_title= OrderAgreement.objects.filter(agreement__title__icontains=term).distinct()[:10]
	if order_agreement_objects_by_title.exists() and can_add(matched_data):
		qs = order_agreement_objects_by_title
		# d = {}
		# d['model_name'] = 'OrderAgreement'
		# d['queryset'] = list(qs.values('id'))
		# d['matched_with'] = 'agreement__title'
		# matched_data.append(d)

		for obj in qs:
			d = {}
			d['model_name'] = 'OrderAgreement'
			d['obj'] = obj.id
			d['matched_with'] = 'agreement__title'
			d['link_text'] = 'agreement In Agreement Orders'
			d['link_text1'] = 'agreement with title: '
			d['link_text2'] = 'In Agreement Orders'
			if can_add(matched_data):
				matched_data.append(d)


	order_agreement_objects_by_category_name = OrderAgreement.objects.filter(agreement__aggrement_category__name__icontains=term).distinct()[:10]
	if order_agreement_objects_by_category_name.exists() and can_add(matched_data):
		qs = order_agreement_objects_by_category_name
		# d = {}
		# d['model_name'] = 'OrderAgreement'
		# d['queryset'] = list(qs.values('id'))
		# d['matched_with'] = 'agreement__aggrement_category__name'
		# matched_data.append(d)
		for obj in qs:
			d = {}
			d['model_name'] = 'OrderAgreement'
			d['obj'] = obj.id
			d['matched_with'] = 'agreement__aggrement_category__name'
			d['link_text'] = 'agreement-category In Agreement Order'
			d['link_text1'] = 'order with agreement-category-name: '
			d['link_text2'] = 'In Agreement Orders'
			if can_add(matched_data):
				matched_data.append(d)
	

	order_service_objects_by_category_name = OrderService.objects.filter(service__category__name__icontains=term).distinct()[:10]
	if order_service_objects_by_category_name.exists() and can_add(matched_data): 
		qs = order_service_objects_by_category_name
		# d = {}
		# d['model_name'] = 'OrderService'
		# d['queryset'] = list(qs.values('id'))
		# d['matched_with'] = 'service__category__name'
		# matched_data.append(d)

		for obj in qs:
			d = {}
			d['model_name'] = 'OrderService'
			d['obj'] = obj.id
			d['matched_with'] = 'service__category__name'
			d['link_text'] = 'service-category In Service Order'
			d['link_text1'] = 'order with service-category-name: '
			d['link_text2'] = 'In Service Orders'
			if can_add(matched_data):
				matched_data.append(d)

	
	order_service_objects_by_sub_category_name = OrderService.objects.filter(service__sub_category__name__icontains=term).distinct()[:10]
	if order_service_objects_by_sub_category_name.exists() and can_add(matched_data):
		qs = order_service_objects_by_sub_category_name
		# d = {}
		# d['model_name'] = 'OrderService'
		# d['queryset'] = list(qs.values('id'))
		# d['matched_with'] = 'service__sub_category__name'
		# matched_data.append(d)

		for obj in qs:
			d = {}
			d['model_name'] = 'OrderService'
			d['obj'] = obj.id
			d['matched_with'] = 'service__sub_category__name'
			d['link_text'] = 'service-subcategory In Service Order'
			d['link_text1'] = 'order with service-subcategory-name: '
			d['link_text2'] = 'In Service Orders'
			if can_add(matched_data):
				matched_data.append(d)


	agreement_objects_by_title = Aggrement.objects.filter(title__icontains=term).distinct()[:10]
	if agreement_objects_by_title.exists() and can_add(matched_data):
		qs = agreement_objects_by_title
		# d = {}
		# d['model_name'] = 'Aggrement'
		# d['queryset'] = [aggrement.to_dict() for aggrement in qs]
		# d['matched_with'] = 'title'
		# matched_data.append(d)

		for obj in qs:
			d = {}
			d['model_name'] = 'Aggrement'
			d['obj'] = obj.to_dict()
			d['matched_with'] = 'title'
			d['link_text'] = 'agreement In Agreement'
			d['link_text1'] = 'agreement with title: '
			d['link_text2'] = 'In Agreements'
			if can_add(matched_data):
				matched_data.append(d)


	order_agreement_objects_by_user_username = OrderAgreement.objects.filter(user__user__username__icontains=term).distinct()[:10]
	if order_agreement_objects_by_user_username.exists() and can_add(matched_data):
		qs = order_agreement_objects_by_user_username
		# d = {}
		# d['model_name'] = 'OrderAgreement'
		# d['queryset'] = list(qs.values('id'))
		# d['matched_with'] = 'user__id'
		# matched_data.append(d)

		# =====
		for obj in qs:
			d = {}
			d['model_name'] = 'OrderAgreement'
			d['obj'] = obj.id
			d['matched_with'] = 'user__user__username'
			d['link_text'] = 'user In Agreement Order'
			d['link_text1'] = 'order with user-username: '
			d['link_text2'] = 'In Agreement Orders'
			if can_add(matched_data):
				matched_data.append(d)


	order_agreement_objects_by_user_firstname = OrderAgreement.objects.filter(user__first_name__icontains=term).distinct()[:10]
	if order_agreement_objects_by_user_firstname.exists() and can_add(matched_data):
		qs = order_agreement_objects_by_user_firstname
		# d = {}
		# d['model_name'] = 'OrderAgreement'
		# d['queryset'] = list(qs.values('id'))
		# d['matched_with'] = 'user__id'
		# matched_data.append(d)

		# =====
		for obj in qs:
			d = {}
			d['model_name'] = 'OrderAgreement'
			d['obj'] = obj.id
			d['matched_with'] = 'user__first_name'
			d['link_text'] = 'user In Agreement Order'
			d['link_text1'] = 'order with user-firstname: '
			d['link_text2'] = 'In Agreement Orders'
			if can_add(matched_data):
				matched_data.append(d)


	order_agreement_objects_by_user_lastname = OrderAgreement.objects.filter(user__last_name__icontains=term).distinct()[:10]
	if order_agreement_objects_by_user_lastname.exists() and can_add(matched_data):
		qs = order_agreement_objects_by_user_lastname
		# d = {}
		# d['model_name'] = 'OrderAgreement'
		# d['queryset'] = list(qs.values('id'))
		# d['matched_with'] = 'user__id'
		# matched_data.append(d)

		# =====
		for obj in qs:
			d = {}
			d['model_name'] = 'OrderAgreement'
			d['obj'] = obj.id
			d['matched_with'] = 'user__last_name'
			d['link_text'] = 'user In Agreement Order'
			d['link_text1'] = 'order with user-lastname: '
			d['link_text2'] = 'In Agreement Orders'
			if can_add(matched_data):
				matched_data.append(d)
	

	order_agreement_objects_by_user_phone = OrderAgreement.objects.filter(user__phone_number__icontains=term).distinct()[:10]
	if order_agreement_objects_by_user_phone.exists() and can_add(matched_data):
		qs = order_agreement_objects_by_user_phone
		# d = {}
		# d['model_name'] = 'OrderAgreement'
		# d['queryset'] = list(qs.values('id'))
		# d['matched_with'] = 'user__id'
		# matched_data.append(d)

		# =====
		for obj in qs:
			d = {}
			d['model_name'] = 'OrderAgreement'
			d['obj'] = obj.id
			d['matched_with'] = 'user__phone_number'
			d['link_text'] = 'user In Agreement Order'
			d['link_text1'] = 'order with user-phone number: '
			d['link_text2'] = 'In Agreement Orders'
			if can_add(matched_data):
				matched_data.append(d)


	order_service_objects_by_user_username = OrderService.objects.filter(user__user__username__icontains=term).distinct()[:10]
	if order_service_objects_by_user_username.exists() and can_add(matched_data):
		qs = order_service_objects_by_user_username
		# d = {}
		# d['model_name'] = 'OrderService'
		# d['queryset'] = list(qs.values('id'))
		# d['matched_with'] = 'user__id'
		# matched_data.append(d)

		#===
		for obj in qs:
			d = {}
			d['model_name'] = 'OrderService'
			d['obj'] = obj.id
			d['matched_with'] = 'user__user__username'
			d['link_text'] = 'user In Service Order'
			d['link_text1'] = 'order with user-username: '
			d['link_text2'] = 'In Service Orders'
			if can_add(matched_data):
				matched_data.append(d)
	

	order_service_objects_by_user_first_name = OrderService.objects.filter(user__first_name__icontains=term).distinct()[:10]
	if order_service_objects_by_user_first_name.exists() and can_add(matched_data):
		qs = order_service_objects_by_user_first_name
		# d = {}
		# d['model_name'] = 'OrderService'
		# d['queryset'] = list(qs.values('id'))
		# d['matched_with'] = 'user__id'
		# matched_data.append(d)

		#===
		for obj in qs:
			d = {}
			d['model_name'] = 'OrderService'
			d['obj'] = obj.id
			d['matched_with'] = 'user__first_name'
			d['link_text'] = 'user In Service Order'
			d['link_text1'] = 'order with user-firstname: '
			d['link_text2'] = 'In Service Orders'
			if can_add(matched_data):
				matched_data.append(d)


	order_service_objects_by_user_last_name = OrderService.objects.filter(user__last_name__icontains=term).distinct()[:10]
	if order_service_objects_by_user_last_name.exists() and can_add(matched_data):
		qs = order_service_objects_by_user_last_name
		# d = {}
		# d['model_name'] = 'OrderService'
		# d['queryset'] = list(qs.values('id'))
		# d['matched_with'] = 'user__id'
		# matched_data.append(d)

		#===
		for obj in qs:
			d = {}
			d['model_name'] = 'OrderService'
			d['obj'] = obj.id
			d['matched_with'] = 'user__last_name'
			d['link_text'] = 'user In Service Order'
			d['link_text1'] = 'order with user-lastname: '
			d['link_text2'] = 'In Service Orders'
			if can_add(matched_data):
				matched_data.append(d)


		# order_service_objects_by_user_email = OrderService.objects.filter(user__email=term).distinct()[:10]
		# if order_service_objects_by_user_email.exists() and can_add(matched_data):
		# 	qs = order_service_objects_by_user_email
		# 	# d = {}
		# 	# d['model_name'] = 'OrderService'
		# 	# d['queryset'] = list(qs.values('id'))
		# 	# d['matched_with'] = 'user__id'
		# 	# matched_data.append(d)
   
		# 	#===
		# 	for obj in qs:
		# 		d = {}
		# 		d['model_name'] = 'OrderService'
		# 		d['obj'] = obj.id
		# 		d['matched_with'] = 'user__email'
		# 		d['link_text'] = 'user In Service Order'
		# 		d['link_text1'] = 'order with user-email: '
		# 		d['link_text2'] = 'In Service Orders'
		# 		if can_add(matched_data):
		# 			matched_data.append(d)

	
	order_service_objects_by_user_phone = OrderService.objects.filter(user__phone_number__icontains=term).distinct()[:10]
	if order_service_objects_by_user_phone.exists() and can_add(matched_data):
		qs = order_service_objects_by_user_phone
		# d = {}
		# d['model_name'] = 'OrderService'
		# d['queryset'] = list(qs.values('id'))
		# d['matched_with'] = 'user__id'
		# matched_data.append(d)

		#===
		for obj in qs:
			d = {}
			d['model_name'] = 'OrderService'
			d['obj'] = obj.id
			d['matched_with'] = 'user__phone_number'
			d['link_text'] = 'user In Service Order'
			d['link_text1'] = 'order with user-phone number: '
			d['link_text2'] = 'In Service Orders'
			if can_add(matched_data):
				matched_data.append(d)


	get_quote_service_objects_by_user_user_name = GetQuoteService.objects.filter(user__user__username__icontains=term).distinct()[:10]
	if get_quote_service_objects_by_user_user_name.exists() and can_add(matched_data):
		qs = get_quote_service_objects_by_user_user_name
		# d = {}
		# d['model_name'] = 'GetQuoteService'
		# d['queryset'] = list(qs.values('id'))
		# d['matched_with'] = 'user__id'
		# matched_data.append(d)

		#===
		for obj in qs:
			d = {}
			d['model_name'] = 'GetQuoteService'
			d['obj'] = obj.id
			d['matched_with'] = 'user__user__username'
			d['link_text'] = 'user In Get-Quote-Service'
			d['link_text1'] = 'get-quote-service with user-username: '
			d['link_text2'] = 'In GetQuoteServices'
			if can_add(matched_data):
				matched_data.append(d)


	get_quote_service_objects_by_user_first_name = GetQuoteService.objects.filter(user__first_name__icontains=term).distinct()[:10]
	if get_quote_service_objects_by_user_first_name.exists() and can_add(matched_data):
		qs = get_quote_service_objects_by_user_first_name
		# d = {}
		# d['model_name'] = 'GetQuoteService'
		# d['queryset'] = list(qs.values('id'))
		# d['matched_with'] = 'user__id'
		# matched_data.append(d)

		#===
		for obj in qs:
			d = {}
			d['model_name'] = 'GetQuoteService'
			d['obj'] = obj.id
			d['matched_with'] = 'user__first_name'
			d['link_text'] = 'user In Get-Quote-Service'
			d['link_text1'] = 'get-quote-service with user-firstname: '
			d['link_text2'] = 'In GetQuoteServices'
			if can_add(matched_data):
				matched_data.append(d)
	

	get_quote_service_objects_by_user_last_name = GetQuoteService.objects.filter(user__last_name__icontains=term).distinct()[:10]
	if get_quote_service_objects_by_user_last_name.exists() and can_add(matched_data):
		qs = get_quote_service_objects_by_user_last_name
		# d = {}
		# d['model_name'] = 'GetQuoteService'
		# d['queryset'] = list(qs.values('id'))
		# d['matched_with'] = 'user__id'
		# matched_data.append(d)

		#===
		for obj in qs:
			d = {}
			d['model_name'] = 'GetQuoteService'
			d['obj'] = obj.id
			d['matched_with'] = 'user__last_name'
			d['link_text'] = 'user In Get-Quote-Service'
			d['link_text1'] = 'get-quote-service with user-lastname: '
			d['link_text2'] = 'In GetQuoteServices'
			if can_add(matched_data):
				matched_data.append(d)


	get_quote_service_objects_by_user_phone_number = GetQuoteService.objects.filter(user__phone_number__icontains=term).distinct()[:10]
	if get_quote_service_objects_by_user_phone_number.exists() and can_add(matched_data):
		qs = get_quote_service_objects_by_user_phone_number
		# d = {}
		# d['model_name'] = 'GetQuoteService'
		# d['queryset'] = list(qs.values('id'))
		# d['matched_with'] = 'user__id'
		# matched_data.append(d)

		#===
		for obj in qs:
			d = {}
			d['model_name'] = 'GetQuoteService'
			d['obj'] = obj.id
			d['matched_with'] = 'user__phone_number'
			d['link_text'] = 'user In Get-Quote-Service'
			d['link_text1'] = 'get-quote-service with user-phone number: '
			d['link_text2'] = 'In GetQuoteServices'
			if can_add(matched_data):
				matched_data.append(d)


	term = urllib.parse.unquote(term)  # for converting emails "rahul%40gmail.com" to "<EMAIL>"
	order_agreement_objects_by_user_email = OrderAgreement.objects.filter(user__email__icontains=term).distinct()[:10]
	if order_agreement_objects_by_user_email.exists() and can_add(matched_data):
		qs = order_agreement_objects_by_user_email
		# d = {}
		# d['model_name'] = 'OrderAgreement'
		# d['queryset'] = list(qs.values('id'))
		# d['matched_with'] = 'user__id'
		# matched_data.append(d)

		# =====
		for obj in qs:
			d = {}
			d['model_name'] = 'OrderAgreement'
			d['obj'] = obj.id
			d['matched_with'] = 'user__email'
			d['link_text'] = 'user In Agreement Order'
			d['link_text1'] = 'order with user-email: '
			d['link_text2'] = 'In Agreement Orders'
			if can_add(matched_data):
				matched_data.append(d)
	

	order_service_objects_by_user_email = OrderService.objects.filter(user__email__icontains=term).distinct()[:10]
	if order_service_objects_by_user_email.exists() and can_add(matched_data):
		qs = order_service_objects_by_user_email
		# d = {}
		# d['model_name'] = 'OrderService'
		# d['queryset'] = list(qs.values('id'))
		# d['matched_with'] = 'user__id'
		# matched_data.append(d)

		#===
		for obj in qs:
			d = {}
			d['model_name'] = 'OrderService'
			d['obj'] = obj.id
			d['matched_with'] = 'user__email'
			d['link_text'] = 'user In Service Order'
			d['link_text1'] = 'order with user-email: '
			d['link_text2'] = 'In Service Orders'
			if can_add(matched_data):
				matched_data.append(d)

	
	get_quote_service_objects_by_user_email = GetQuoteService.objects.filter(user__email__icontains=term).distinct()[:10]
	if get_quote_service_objects_by_user_email.exists() and can_add(matched_data):
		qs = get_quote_service_objects_by_user_email
		# d = {}
		# d['model_name'] = 'GetQuoteService'
		# d['queryset'] = list(qs.values('id'))
		# d['matched_with'] = 'user__id'
		# matched_data.append(d)

		#===
		for obj in qs:
			d = {}
			d['model_name'] = 'GetQuoteService'
			d['obj'] = obj.id
			d['matched_with'] = 'user__email'
			d['link_text'] = 'user In Get-Quote-Service'
			d['link_text1'] = 'get-quote-service with user-email: '
			d['link_text2'] = 'In GetQuoteServices'
			if can_add(matched_data):
				matched_data.append(d)



	# Initialize the total number of objects in a queryset to keep
	# remaining_queryset = 10
	# result_data = []

	# # Iterate through each model
	# for model in matched_data:
	# 	if remaining_queryset > 0:
	# 		# Calculate the number of queryset_objects to take from the current model
	# 		num_queryset_objects_to_take = min(len(model['queryset']), remaining_queryset)
	# 		# Take the required number of queryset_objects from the current model
	# 		taken_queryset_objects = model['queryset'][:num_queryset_objects_to_take]
	# 		# Add the model with the taken queryset_objects to the result data
	# 		result_data.append({
	# 			'model_name': model['model_name'],
	# 			'queryset': taken_queryset_objects,
	# 			'matched_with': model['matched_with']
	# 		})
	# 		# Decrease the remaining queryset_objects count
	# 		remaining_queryset -= num_queryset_objects_to_take
	# 	else:
	# 		break
	# print("=========== result data : ",result_data)
	
	# return JsonResponse({'matched_data': matched_data}, safe=False)

	# print("=========== result data : ",matched_data)
	return matched_data



def search_dropdown(request):

	term = request.GET.get('term')
	
	if not term:
		return JsonResponse({'error': 'No search term provided'}, status=400)
	
	r = get_search_suggestions(term)
	matched_data = r

	context = {
		'matched_data' : matched_data,
		'term': term
	}

	if request.is_ajax():
		html = render_to_string('master_admin/search_dropdown.html',
								context, request=request)

	data = {
		'html': html,
		'term': term,
	}
	return JsonResponse(data)



def search_results(request):
	term = request.GET.get('term')
	if not term:
		return JsonResponse({'error': 'No search term provided'}, status=400)
	result_data = get_search_suggestions(term)
	matched_data = result_data
	context = {
		'term': term,
		'matched_data' : matched_data
	}
	return render(request, 'master_admin/search_results.html', context)


def admin_dashboard_get_most_used_legal_agreements(request):
	most_ordered = Aggrement.objects.annotate(
		order_count=Count('agreement_orderAgreements')
	).filter(order_count__gt=0).order_by('-order_count')
	context={
		'most_ordered_agreements' : most_ordered
	}
	return render(request,"master_admin/most_used_agreements.html", context)


def admin_dashboard_change_order_status_for_order(request, order_type, order_id):
	context = {}

	if order_type == "agreement":

		if not OrderAgreement.objects.filter(id=order_id).exists():
			return CommonMixin.render(request, '404.html' ,context)
		
		order = OrderAgreement.objects.filter(id=order_id).first()
		
		if (order.agreement_service_option == 'make_with_ai' and order.order_status == "PENDING") or (order.agreement_service_option == 'make_with_legal_help' and order.order_status == "PENDING"):
			order.order_status = "COMPLETED"
			order.save()
			ag = Aggrement.objects.filter(id=order.agreement.id).first()
			if order.agreement_service_option == 'make_with_ai':
				ag.make_with_ai_count += 1
			elif order.agreement_service_option == 'make_with_legal_help':
				ag.make_with_legal_help_count += 1
				ag.save()

			agreement_slug = ag.slug
			agreement_service_option = order.agreement_service_option

			url_PlaceAgreementOrder = request.scheme + "://" + request.get_host() +  reverse('aggrement:checkout', args=[agreement_slug, agreement_service_option])
			if order.user.email:
				user_email = order.user.email
			else:
				user_email = order.user.user.email
		
			thread = threading.Thread(target=send_email_for_completed_order_status_for_agreement_order, args=(order, user_email, url_PlaceAgreementOrder))
			thread.start()
			messages.success(request, f'Order status changed successfully for order ID: {order.id}')
			return redirect('admin_dashboard_orders', order_type=order_type)
		else:
			return CommonMixin.render(request, '404.html' ,context)

	elif order_type == "service":
	
		if not OrderService.objects.filter(id=order_id).exists():
			return CommonMixin.render(request, '404.html' ,context)
		
		order = OrderService.objects.filter(id=order_id).first()
		order.order_status = "COMPLETED"
		order.save()
		url_PlaceServiceOrder = request.scheme + "://" + request.get_host() +  reverse('services:checkout', args=[order.service.slug])
		if order.user.email:
			user_email = order.user.email
		else:
			user_email = order.user.user.email

		thread = threading.Thread(target=send_email_for_order_completed_service_order, args=(order, user_email, url_PlaceServiceOrder))
		thread.start()
		messages.success(request, f'Order status changed successfully for order ID: {order.id}')
		return redirect('admin_dashboard_orders', order_type=order_type)
	else:
		return CommonMixin.render(request, '404.html' ,context)


class GroupConcat(Aggregate):
	function = 'GROUP_CONCAT'
	template = "%(function)s(%(distinct)s%(expressions)s SEPARATOR '%(separator)s')"

	def __init__(self, expression, distinct=False, separator=',', **extra):
		distinct = 'DISTINCT ' if distinct else ''
		super().__init__(
			expression,
			separator=separator,
			distinct=distinct,
			output_field=CharField(),
			**extra
		)


class AgreementURL(Func):
	function = 'CONCAT'
	template = "%(function)s(%(expressions)s)"

	def __init__(self, slug, **extra):
		# Use a placeholder for the slug
		url_pattern = reverse('aggrement:agreement_details', kwargs={'agreement_slug': 'PLACEHOLDER'})
		url_base = url_pattern.replace('PLACEHOLDER', '').rstrip('/')  # Ensure no trailing slash after url_pattern
		expressions = [
			Value(url_base + '/'),  # Add the trailing slash here
			slug,
			Value('')  
		]
		super().__init__(*expressions, output_field=CharField(), **extra)

	def as_sql(self, compiler, connection):
		# Override as_sql to handle any database-specific syntax if needed
		sql, params = super().as_sql(compiler, connection)
		# Replace 'PLACEHOLDER' with the actual column reference
		sql = sql.replace("'PLACEHOLDER'", "%s")
		return sql, params


class ServiceURL(Func):
    function = 'CONCAT'
    template = "%(function)s(%(expressions)s)"

    def __init__(self, category_slug, service_slug, **extra):
        # Use placeholders for the slugs and remove the trailing slash
        url_pattern = reverse('services:services_details2', kwargs={'category_slug': 'PLACEHOLDER_CAT', 'service_slug': 'PLACEHOLDER_SER'})
        url_base = url_pattern.replace('PLACEHOLDER_CAT', '').replace('PLACEHOLDER_SER', '').rstrip('/')
        expressions = [
            Value(url_base + '/'),  # Add the trailing slash here
            category_slug,
            Value('/'),
            service_slug,
            Value('/')  
        ]
        super().__init__(*expressions, output_field=CharField(), **extra)

    def as_sql(self, compiler, connection):
        # Override as_sql to handle any database-specific syntax if needed
        sql, params = super().as_sql(compiler, connection)
        # Replace 'PLACEHOLDER' with the actual column reference
        sql = sql.replace("'PLACEHOLDER_CAT'", "%s").replace("'PLACEHOLDER_SER'", "%s")
        return sql, params


def get_orders_for_sales_notifications(request):
	
	agreement_orders_list = []
	service_orders_list = []
	combined_list = []
	start_str = request.scheme + "://" + request.get_host() 
	agreement_orders = OrderAgreement.objects.filter(payment_status="PAYMENT_SUCCESS").order_by('?').prefetch_related('agreement__aggrement_category')
	service_orders = OrderService.objects.filter(payment_status="captured").order_by('?')
	if agreement_orders.exists():
		end_str = "?category="
		agreement_orders_qs = agreement_orders.values(
			'id', 'user__first_name', 'agreement__title', 'user__state__state_name'
		).annotate(
			category_string=Coalesce(
				GroupConcat('agreement__aggrement_category__slug', separator=','),
				Value('general')
			)
		).annotate(
			url_str=Concat(
				Value(end_str),
				F('category_string'),
				output_field=CharField()
			)
		).annotate(
			agreement_url=Concat(
				Value(start_str),
				AgreementURL(F('agreement__slug')),
				F('url_str'),
				output_field=CharField()
			)
		).annotate(
			order_type=Value('agreement', output_field=CharField())
		).values(
			'id', 'user__first_name', 'agreement__title', 'user__state__state_name', 'order_type', 'agreement_url'
		)

		agreement_orders_list = list(agreement_orders_qs)
		combined_list += agreement_orders_list
		
	if service_orders.exists():
		
		service_orders_qs = service_orders.values(
			'id', 'user__first_name', 'service__service_title', 'user__state__state_name'
		).annotate(
			service_url=Concat(
				Value(start_str),
				ServiceURL(F('service__category__slug'), F('service__slug')),
				output_field=CharField()
			)
		).annotate(
			order_type=Value('service', output_field=CharField())
		).values(
			'id', 'user__first_name', 'service__service_title', 'user__state__state_name', 'order_type', 'service_url'
		)

		service_orders_list = list(service_orders_qs)
		combined_list += service_orders_list

	if combined_list:
		random.shuffle(combined_list)
		return JsonResponse({'orders_exist': True, 'orders': combined_list})
	else:
		return JsonResponse({'orders_exist': False})




# ++++++++++++++++++++++++++ EXTRA +++++++++++++++++++++++++++++++++
# from aggrement.models import OrderAgreement
# from django.http import HttpResponse
# from aggrement.views import get_total_orders_count
# import json
# def reset_order_status(request):
	
	# agreements = OrderAgreement.objects.filter(
	#     agreement_service_option__in=['word', 'pdf'],
	#     payment_status='PAYMENT_SUCCESS'
	# )
	# agreements.update(order_status='COMPLETED')
	
	# services = OrderService.objects.all()
	# services.update(order_status='PENDING')
	# return HttpResponse("Done")
	# ===================================================================
	# context = get_total_orders_count()
	
	
	# total_completed_amount_word = OrderAgreement.objects.filter(
	# 	agreement_service_option='word',
	# 	payment_status="PAYMENT_SUCCESS" #order_status="COMPLETED"
	# ).count()

	# context['new_word_total_pay_success'] = total_completed_amount_word
 
 
	# total_completed_amount_word = OrderAgreement.objects.filter(
	# 	agreement_service_option='word',
	# 	payment_status="PAYMENT_SUCCESS" #order_status="COMPLETED"
	# )
	# l = list()
	# for q in total_completed_amount_word:
	# 	l.append(q.id)
	# context['myList'] = l
 
 
 
	# ================= get no. of order agreements without phone ==========
	
	# without_phone_count = OrderAgreement.objects.filter(user__phone_number__isnull=True).count()
	# context = {}
	# context['without_phone_count'] = without_phone_count
 
	# # Convert the dictionary to a JSON string for better readability
	# data_string = json.dumps(context, indent=4)
	# return HttpResponse(data_string, content_type='text/plain')