"""
Forms
"""
from django.contrib.auth import get_user_model
from django.contrib.auth.forms import UserCreationForm, AuthenticationForm
from django.core.exceptions import ValidationError
from django import forms
from django.db.models import Q
from allauth.socialaccount.models import SocialAccount
from django.contrib.auth import authenticate
from django.utils.translation import gettext_lazy as _
from django.core.validators import validate_email
from django.contrib.auth.models import User


class CreateUserForm(UserCreationForm):
    """
    Create User Form
    """
    email = forms.EmailField()
    term_accepted = forms.BooleanField()

    class Meta:
        fields = ("username", "email", "first_name", "last_name", "password1",
                  "password2", "term_accepted")
        model = get_user_model()

    def __init__(self, *args, **kwargs):
        super(CreateUserForm, self).__init__(*args, **kwargs)
    

        self.fields['email'].required = True
        self.fields['username'].widget.attrs.update(
            {'class' : 'form-control','placeholder': 'Enter User Name'})
        self.fields['email'].widget.attrs.update(
            {'class' : 'form-control','placeholder': 'Enter Email'})
        self.fields['first_name'].required = True
        self.fields['first_name'].widget.attrs.update(
            {'class' : 'form-control','placeholder': 'Enter First Name'})
        self.fields['last_name'].required = True
        self.fields['last_name'].widget.attrs.update(
            {'class' : 'form-control','placeholder': 'Enter Last Name'})
        self.fields['password1'].widget.attrs.update(
            {'class' : 'form-control','placeholder': 'Enter Password'})
        self.fields['password2'].widget.attrs.update(
            {'class' : 'form-control','placeholder': 'Confirm Password'})

        for fieldname in ['username', 'password1', 'password2']:
            self.fields[fieldname].help_text = None

    def clean_email(self):
        email = self.cleaned_data.get('email')
        username = self.cleaned_data.get('username')
        if email and get_user_model().objects.filter(email=email).exclude(username=username).exists():
            raise ValidationError(u'This Email address was already registered')

        domain = email.split('@')[1]
        domain_list = ["yandex.com", "smotretonline2015.ru"]
        if domain in domain_list:
            raise ValidationError(u'Please use a valid email address')
            
        return email

    def clean_term_accepted(self):
        '''Here we can check if the checkbox is checked or not'''
        term_accepted = self.cleaned_data.get('term_accepted')
        if not term_accepted:
            # Raise an error if the checkbox is not checked
            raise ValidationError("You must select this option to proceed")
        # And return the value
        return term_accepted




class CustomAuthenticationForm(AuthenticationForm):
    error_messages = dict(AuthenticationForm.error_messages,
                        valid_social_account=_(
            "Our records show that you have a google authentication with this email. Please login with google to access your account or use other email."
        ))
    
    def clean(self):
        username = self.cleaned_data.get('username')
        password = self.cleaned_data.get('password')
        
        if username is not None and password:
            # Check if username is a valid email address
            try:
                validate_email(username)
                user_obj = User.objects.filter(email=username)
                if user_obj.exists():
                    user_username = user_obj.first().username
                    self.user_cache = authenticate(self.request, username=user_username, password=password)
            except ValidationError:
                self.user_cache = authenticate(self.request, username=username, password=password)
                
            if self.user_cache is None:
                obj = SocialAccount.objects.filter(Q(user__email=username) | Q(user__username=username))
                if obj.exists():
                    raise self.get_valid_social_account_error()
                else:
                    raise self.get_invalid_login_error()
            else:
                self.confirm_login_allowed(self.user_cache)
        
        return self.cleaned_data
    
    
    def get_valid_social_account_error(self):
        return forms.ValidationError(
            self.error_messages['valid_social_account'],
            code='valid_social_account',
            params={'username': self.username_field.verbose_name},
        )

