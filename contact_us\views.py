import json
import threading
import urllib
from django.shortcuts import render, redirect
from django.urls import reverse
from django.http import HttpResponseRedirect
from django.contrib import messages
from django.conf import settings
from django.template.loader import get_template
from django.core.mail import EmailMultiAlternatives
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
from app.common import CommonMixin
from contract_seo.models import CommonSeo
from email_configuration.views import ask_question_mail, send_contact_us_inquiry_email
from .forms import QueryFrom<PERSON>ontactForm, UserQuestionsForm, AnswerForm
from .models import QueryFromContact, ContactUs, UserQuestions, AreaOfLaw, City
from sample.settings import DEFAULT_FROM_EMAIL as ADMIN_EMAIL
# Create your views here.

def send_contact_message_query_mail(contact):
    """
    Send Service Contact Mail
    """
    subject = contact.subject

    plaintext = get_template('contact_query/query.txt')

    htmly = get_template('contact_query/query.html')

    context = { 'contact' : contact }

    from_email = settings.DEFAULT_FROM_EMAIL

    text_content = plaintext.render(context)
    html_content = htmly.render(context)

    msg = EmailMultiAlternatives(subject, text_content, from_email, ['<EMAIL>'])
    msg.attach_alternative(html_content, "text/html")
    msg.send()


def send_user_question_mail(questions):
    """
    Send User Question Mail
    """
    subject = questions.subject
    plaintext = get_template('user_questions/question_mail.txt')
    htmly = get_template('user_questions/question_mail.html')
    context = { 'questions' : questions }
    from_email = settings.DEFAULT_FROM_EMAIL
    text_content = plaintext.render(context)
    html_content = htmly.render(context)
    msg = EmailMultiAlternatives(subject, text_content, from_email, ['<EMAIL>'])
    msg.attach_alternative(html_content, "text/html")
    msg.send()



def send_lawyer_mail(questions):
    """
    Send User Question Mail
    """
    subject = 'Query From Talk To Lawyer Page'

    plaintext = get_template('lawyer_query/question_mail.txt')

    htmly = get_template('lawyer_query/question_mail.html')

    context = { 'questions' : questions }

    from_email = settings.DEFAULT_FROM_EMAIL

    text_content = plaintext.render(context)
    html_content = htmly.render(context)

    msg = EmailMultiAlternatives(subject, text_content, from_email, ['<EMAIL>'])
    msg.attach_alternative(html_content, "text/html")
    msg.send()



def send_email_for_ContactUs_inquiry(obj, user_email, url_ContactUs):
    # send email to customer
    send_contact_us_inquiry_email(obj, user_email, 'CONTACT-US-INQUIRY-MAIL')
    # send email to admin   
    send_contact_us_inquiry_email(obj, ADMIN_EMAIL, 'ADMIN-CONTACT-US-INQUIRY-MAIL', url_ContactUs)


def contact_us_view(request):  # using now
    """
    Contact Us View
    """
    contact = ContactUs.objects.filter(sample__exact='CONTACT_US').first() # sample__exact='CONTACT_US' 'Contract Easily'
 
    if request.method == "POST":
        query_form = QueryFromContactForm(request.POST or None)
        if query_form.is_valid():
            name = request.POST.get('name')
            email = request.POST.get('email')
            phone_no = request.POST.get('phone_no')
            subject = request.POST.get('subject')
            message = request.POST.get('message')

            # Begin reCAPTCHA validation
            recaptcha_response = request.POST.get('g-recaptcha-response')
            url = 'https://www.google.com/recaptcha/api/siteverify'
            values = {
                'secret': settings.GOOGLE_RECAPTCHA_SECRET_KEY,
                'response': recaptcha_response
            }
            data = urllib.parse.urlencode(values).encode()
            req =  urllib.request.Request(url, data=data)
            response = urllib.request.urlopen(req)
            result = json.loads(response.read().decode())
            # End reCAPTCHA validation

            if result['success']:
                answer = QueryFromContact.objects.create(
                    name=name, email=email, phone_no=phone_no, subject=subject, message=message)
                answer.save()
                # send_contact_message_query_mail(contact=answer) # not in use
                url_ContactUs = request.scheme + "://" + request.get_host() +  reverse('contact_us:contact_query')
                thread = threading.Thread(target=send_email_for_ContactUs_inquiry, args=(answer, answer.email, url_ContactUs))
                thread.start()
                messages.success(
                request, 'Your message received successfully, our team will get back to you soon!')
            else:
                messages.error(request, 'Invalid reCAPTCHA. Please try again.')
                
            return HttpResponseRedirect(request.META.get("HTTP_REFERER"))
    else:
        query_form = QueryFromContactForm()

    seo = CommonSeo.objects.filter(sample__exact='CONTACT-PAGE-SEO').first()
    context = {
        'contact' : contact,
        'form' : query_form,
        'seo' : seo,
    }
    return CommonMixin.render(request, 'contact_us.html', context)



# def contact_us_view(request): # commented now
#     """
#     Contact Us View
#     """
#     contact = ContactUs.objects.filter(sample__exact='Contract Easily').first()
#     seo = CommonSeo.objects.filter(sample__exact='Contract Easily').first()

#     context = {
#         'contact' : contact,
#         'seo' : seo
#     }
#     return CommonMixin.render(request, 'contact_us2.html', context)




def ask_question_view(request):
    """
    Ask Question View
    """ 
    if request.method == "POST":
        query_form = UserQuestionsForm(request.POST or None)
        if query_form.is_valid():
            name = request.POST.get('name')
            email = request.POST.get('email')
            phone_no = request.POST.get('phone_no')
            subject = request.POST.get('subject')
            area_of_interest = AreaOfLaw.objects.filter(id=request.POST.get('area_of_interest')).first()
            city = City.objects.filter(id=request.POST.get('city')).first()
            question = request.POST.get('question')

            # Begin reCAPTCHA validation
            recaptcha_response = request.POST.get('g-recaptcha-response')
            url = 'https://www.google.com/recaptcha/api/siteverify'
            values = {
                'secret': settings.GOOGLE_RECAPTCHA_SECRET_KEY,
                'response': recaptcha_response
            }
            data = urllib.parse.urlencode(values).encode()
            req =  urllib.request.Request(url, data=data)
            response = urllib.request.urlopen(req)
            result = json.loads(response.read().decode())
            # End reCAPTCHA validation

            if result['success']:
                answer = UserQuestions.objects.create(
                    name=name, email=email, phone_no=phone_no, subject=subject, area_of_interest=area_of_interest, city=city, question=question)
                answer.save()
                ask_question_mail(question=answer)
                messages.success(
                    request, 'Your Question is send successfully, our team will get back to you soon!')

            else:
                messages.error(request, 'Invalid reCAPTCHA. Please try again.')

            return HttpResponseRedirect(request.META.get("HTTP_REFERER"))
    else:
        query_form = UserQuestionsForm()

    seo = CommonSeo.objects.filter(sample__exact='CONTACT-PAGE-SEO').first()

    context = {
        'form' : query_form,
        'seo' : seo
    }
    return CommonMixin.render(request, 'user_free_questions.html', context)






def question_forum(request):
    """
    Question Forum 
    """
    questions = UserQuestions.objects.all().order_by('-id')

    page = request.GET.get('page', 1)

    paginator = Paginator(questions, 12)
    try:
        question_list = paginator.page(page)
    except PageNotAnInteger:
        question_list = paginator.page(1)
    except EmptyPage:
        question_list = paginator.page(paginator.num_pages)

    interest_area = AreaOfLaw.objects.all().order_by('-id')

    city_list = City.objects.all().order_by('city_name')

    seo = CommonSeo.objects.filter(sample__exact='QUESTION-FORUM-SEO').first()

    context = {
        'question_list' : question_list,
        'interest_area' : interest_area,
        'city_list' : city_list,
        'seo' : seo
    }
    return CommonMixin.render(request, 'question_forum.html', context)

def answer_question(request, question_slug):
    """
    Answer Specific Question
    """
    try:
        if request.user.profile.user_type == 'CUSTOMER':
            messages.error(request, 'You have to be a registered LAWYER For Answering Questions')
            return redirect(reverse('contact_us:question_forum'))
    except AttributeError as error:
        messages.error(request, 'You have to be a registered LAWYER For Answering Questions')
        return redirect(reverse('contact_us:question_forum'))

    question_details = UserQuestions.objects.filter(slug=question_slug).first()

    if question_details.is_answered == True:
        messages.error(request, 'This Question is already answered by ' + question_details.answered_by.username)
        return redirect(reverse('contact_us:question_forum'))

    if request.method == 'POST':
        form = AnswerForm(request.POST, instance=question_details)
        if form.is_valid():
            answer = request.POST.get('answer')

            if not answer:
                messages.error(
                    request, 'Answer Cannot be blank')
                return HttpResponseRedirect(request.META.get("HTTP_REFERER"))

            question_details.answer =  answer
            question_details.answered_by = request.user
            question_details.is_answered = True
            question_details.save()
            messages.success(request, 'Answer updated successfully')
            return redirect(reverse('contact_us:question_forum'))
    else:
        form = AnswerForm(instance=question_details)

    seo = CommonSeo.objects.filter(sample__exact='QUESTION-FORUM-SEO').first()


    context = {
        'question_details' : question_details,
        'form' : form,
        'seo' : seo
    }
    return CommonMixin.render(request, 'answer_provide.html', context)





