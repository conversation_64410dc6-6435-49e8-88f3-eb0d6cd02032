"""
Arka (URLs)
"""
from django.conf.urls import url
from django.urls import path, reverse_lazy
from django.contrib.auth import views as auth_views
from . import views

app_name = 'accounts'

urlpatterns = [
	
	url(r"signup/$", views.signup_view, name="signup"),
	url(r'login/$', views.CustomLoginView.as_view(), name="login"),
	url(r"logout/$", views.web_logout_view, name="logout"),

	#change password inside the ecosystem
	
    url(r'^change_password/$', views.change_password_view, name="change_password"),
    
    path("verify-email/<slug:username>", views.verify_email, name="verify_email"),
    path("resend-otp", views.resend_otp, name="resend_otp"),
    
    path('password-reset/', views.CustomPasswordResetView.as_view(), name='password_reset'),
    path('password-reset-done/', auth_views.PasswordResetDoneView.as_view(template_name='password_reset/password_reset_done.html'), name='password_reset_done'),
    path('password-reset-confirm/<uidb64>/<token>/',auth_views.PasswordResetConfirmView.as_view(template_name='password_reset/password_reset_confirm.html', success_url = reverse_lazy('accounts:password_reset_complete')), name='password_reset_confirm'),
    path('password-reset-complete/', auth_views.PasswordResetCompleteView.as_view(template_name='password_reset/password_reset_complete.html'), name='password_reset_complete'),


]