
/* Styles for custom select with search */
.custom-select-container {
    position: relative;
    /* display: inline-block;  */
}
  
.custom-select {
    /* width: 200px;  */
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    cursor: pointer;
}
  
.custom-select-options {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0; 
    z-index: 1;
    display: none;
    max-height: 150px;
    overflow-y: auto;
    border: 1px solid #ccc;
    border-top: none;
    border-radius: 0 0 4px 4px;
    background: #222;
    color: #c2c2c2; 
}

.custom-option {
    padding: 8px;
    cursor: pointer;
    transition: background 0.3s;
}

.custom-option:hover {
    /* background-color: #f0f0f0;  */
    color: #d5aa6d;
}

.search-state-input {
    width: 100%;
    padding: 8px;
    box-sizing: border-box;
    color: #333;
    /* border-color: #d5aa6d; */
    border: 2px solid #d5aa6d;
    outline-color: #d5aa6d;
}