:root {
    --primary: #d5aa6d;  
    /* var(--primary);  ##4e73df  */
}

/* var(--primary) */
a {
    color: var(--primary);
    text-decoration: none;
    background-color: transparent;
}

a:hover {
    color: #000; /* #224abe;  */
    text-decoration: none;/* underline;  */
}

.btn:hover {
    color: #858796;
    text-decoration: none;
}

.btn:focus, .btn.focus {
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.btn-primary {
    color: #fff;
    background-color: var(--primary);
    border-color: var(--primary);
}

.btn-primary:hover {
    color: #fff;
    background-color: #000; /* #2e59d9;  */
    border-color: #000; /* #2653d4;  */
}

.btn-primary:focus, .btn-primary.focus {
    color: #fff;
    background-color: #000; /* #2e59d9;  */
    border-color: #000; /* #2653d4; */
    box-shadow: 0 0 0 0.2rem rgba(105, 136, 228, 0.5);
}

.btn-primary.disabled, .btn-primary:disabled {
    color: #fff;
    background-color: var(--primary);
    border-color: var(--primary);
}

.btn-primary:not(:disabled):not(.disabled):active, .btn-primary:not(:disabled):not(.disabled).active,
.show>.btn-primary.dropdown-toggle {
    color: #fff;
    background-color: #000; /* #2653d4;  */
    border-color: #000; /* #244ec9; */
}

.btn-primary:not(:disabled):not(.disabled):active:focus, .btn-primary:not(:disabled):not(.disabled).active:focus,
.show>.btn-primary.dropdown-toggle:focus {
    box-shadow: 0 0 0 0.2rem rgba(105, 136, 228, 0.5);
}

.btn-outline-primary {
    color: var(--primary);
    border-color: var(--primary);
}

.btn-outline-primary:hover {
    color: #fff;
    background-color: var(--primary);
    border-color: var(--primary);
}

.btn-outline-primary:focus, .btn-outline-primary.focus {
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.5);
}

.btn-outline-primary.disabled, .btn-outline-primary:disabled {
    color: var(--primary);
    background-color: transparent;
}

.btn-outline-primary:not(:disabled):not(.disabled):active, .btn-outline-primary:not(:disabled):not(.disabled).active,
.show>.btn-outline-primary.dropdown-toggle {
    color: #fff;
    background-color: var(--primary);
    border-color: var(--primary);
}

.btn-outline-primary:not(:disabled):not(.disabled):active:focus, .btn-outline-primary:not(:disabled):not(.disabled).active:focus,
.show>.btn-outline-primary.dropdown-toggle:focus {
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.5);
}


.btn-link {
    font-weight: 400;
    color: var(--primary);
    text-decoration: none;
}

.btn-link:hover {
    color: #224abe;
    text-decoration: underline;
}

.custom-control-input:checked~.custom-control-label::before {
    color: #fff;
    border-color: var(--primary);
    background-color: var(--primary);
}

.custom-control-input:focus~.custom-control-label::before {
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.custom-checkbox .custom-control-input:indeterminate~.custom-control-label::before {
    border-color: var(--primary);
    background-color: var(--primary);
}

.custom-checkbox .custom-control-input:disabled:checked~.custom-control-label::before {
    background-color: rgba(78, 115, 223, 0.5);
}

.custom-checkbox .custom-control-input:disabled:indeterminate~.custom-control-label::before {
    background-color: rgba(78, 115, 223, 0.5);
}

.custom-radio .custom-control-input:disabled:checked~.custom-control-label::before {
    background-color: rgba(78, 115, 223, 0.5);
}

.custom-switch .custom-control-input:disabled:checked~.custom-control-label::before {
    background-color: rgba(78, 115, 223, 0.5);
}

.custom-range::-moz-range-thumb {
    width: 1rem;
    height: 1rem;
    background-color: var(--primary);
    border: 0;
    border-radius: 1rem;
    -moz-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    -moz-appearance: none;
    appearance: none;
}

.custom-range::-ms-thumb {
    width: 1rem;
    height: 1rem;
    margin-top: 0;
    margin-right: 0.2rem;
    margin-left: 0.2rem;
    background-color: var(--primary);
    border: 0;
    border-radius: 1rem;
    -ms-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    appearance: none;
}

.nav-pills .nav-link.active,
.nav-pills .show>.nav-link {
    color: #fff;
    background-color: var(--primary);
}

.page-link {
    position: relative;
    display: block;
    padding: 0.5rem 0.75rem;
    margin-left: -1px;
    line-height: 1.25;
    color: var(--primary);
    background-color: #fff;
    border: 1px solid #dddfeb;
}

.page-link:hover {
    z-index: 2;
    color: #224abe;
    text-decoration: none;
    background-color: #eaecf4;
    border-color: #dddfeb;
}

.page-item.active .page-link {
    z-index: 3;
    color: #fff;
    background-color: var(--primary);
    border-color: var(--primary);
}

.badge-primary {
    color: #fff;
    background-color: var(--primary);
}

a.badge-primary:hover, a.badge-primary:focus {
    color: #fff;
    background-color: #2653d4;
}

a.badge-primary:focus, a.badge-primary.focus {
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.5);
}

.alert-primary {
    color: #293c74;
    background-color: #dce3f9;
    border-color: #cdd8f6;
}

.alert-primary .alert-link {
    color: #1c294e;
}


.progress-bar {
    display: flex;
    flex-direction: column;
    justify-content: center;
    overflow: hidden;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    background-color: var(--primary);
    transition: width 0.6s ease;
}

.list-group-item.active {
    z-index: 2;
    color: #fff;
    background-color: var(--primary);
    border-color: var(--primary);
}


.list-group-item-primary {
    color: #293c74;
    background-color: #cdd8f6;
}

.list-group-item-primary.list-group-item-action:hover, .list-group-item-primary.list-group-item-action:focus {
    color: #293c74;
    background-color: #b7c7f2;
}

.list-group-item-primary.list-group-item-action.active {
    color: #fff;
    background-color: #293c74;
    border-color: #293c74;
}



.bg-primary {
    background-color: var(--primary) !important;
}

a.bg-primary:hover, a.bg-primary:focus,
button.bg-primary:hover,
button.bg-primary:focus {
    background-color: #2653d4 !important; 
}


.border-primary {
    border-color: var(--primary) !important;
}

.text-primary {
    color: var(--primary) !important;
}

a.text-primary:hover, a.text-primary:focus {
    color: #224abe !important;
}


.bg-gradient-primary {
    background-color: var(--primary);
    background-image: linear-gradient(180deg, var(--primary) 10%, #224abe 100%);
    background-size: cover;
}


.border-left-primary {
    border-left: 0.25rem solid var(--primary) !important;
}

.border-bottom-primary {
    border-bottom: 0.25rem solid var(--primary) !important;
}

.topbar .dropdown-list .dropdown-header {
    background-color: var(--primary);
    border: 1px solid var(--primary);
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
    color: #fff;
}


.sidebar .nav-item .collapse .collapse-inner .collapse-item.active,
.sidebar .nav-item .collapsing .collapse-inner .collapse-item.active {
    color: var(--primary);
    font-weight: 700;
}


.btn-facebook {
    color: #fff;
    background-color: #3b5998;
    border-color: #fff;
}

.btn-facebook:hover {
    color: #fff;
    background-color: #30497c;
    border-color: #e6e6e6;
}

.btn-facebook:focus, .btn-facebook.focus {
    color: #fff;
    background-color: #30497c;
    border-color: #e6e6e6;
    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.5);
}

.btn-facebook.disabled, .btn-facebook:disabled {
    color: #fff;
    background-color: #3b5998;
    border-color: #fff;
}

.btn-facebook:not(:disabled):not(.disabled):active, .btn-facebook:not(:disabled):not(.disabled).active,
.show>.btn-facebook.dropdown-toggle {
    color: #fff;
    background-color: #2d4373;
    border-color: #dfdfdf;
}


.error:before {
    content: attr(data-text);
    position: absolute;
    left: -2px;
    text-shadow: 1px 0 var(--primary);
    top: 0;
    color: #5a5c69;
    background: #f8f9fc;
    overflow: hidden;
    clip: rect(0, 900px, 0, 0);
    animation: noise-anim-2 3s infinite linear alternate-reverse;
}

/* templates/master_admin/agreement_detail.html, order_detail.html, service_detail.html */
.f-bold {
    font-weight: bold;
}

/* templates/master_admin/index.html START */
.rupee-icon {
    font-size: 1.25rem;
}
.donut-chart-card {
    /* background-color: #fdf1c4; */
    border-radius: 10px;
    /*padding: 20px; */
    padding: 0px;
    text-align: left;
    /* margin: 10px 0; */
    /* box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); */
    display: flex;
    align-items: center;

}
.progress-circle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: conic-gradient(#f05454 25%, #eaeaea 0%);
    margin-right: 20px;
    flex-shrink: 0;
}
.donut-chart-content {
    display: flex;
    /*flex-direction: column; */
    flex-direction: row-reverse;
    width: 100%;
}
.donut-chart-title {
    font-weight: bold;
    font-size: 18px;
    margin-bottom: 10px;
}
.donut-chart-details {
    font-size: 14px;
    color: #666;
    /*display: flex; */
    flex-wrap: wrap;
}
.donut-chart {
    width: 120px;/* 150px  300px; */
    height: 120px;/* 150px 300px; */
    position: relative;
    flex-shrink: 0;
}
.donut-chart-circle {
    fill: none;
    stroke-width: 10;
    transition: stroke-dasharray 0.3s;
    transform: rotate(-90deg);
    transform-origin: 50% 50%;
}
.donut-chart-tooltip {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #fff;
    border: 1px solid #ccc;
    padding: 5px 10px; 
    display: none;
    pointer-events: none;
    width: max-content;
    background-color: rgb(255,255,255);
    color: #858796;
    border-color: #dddfeb;
    border-width: 1px;
    border-radius: 5px;
}

.small-font-size {
    font-size: 1rem;
}

/* templates/master_admin/index.html END */

/* templates/master_admin/orders.html */
nav > .nav.nav-tabs{
    border: none;
    color: #fff;
    background: #222;
    border-radius:0;
}

nav > div a.nav-item.nav-link,
nav > div a.nav-item.nav-link.active
{
    border: none;
    padding: 18px 25px;
    color: #fff;
    background: #222;/* #272e38; */
    border-radius:0;
}

nav > div a.nav-item.nav-link.active:after
{
    content: "";
    position: relative;
    bottom: -60px;
    left: -10%;
    border: 15px solid transparent;
    /* border-top-color: var(--primary); /* down arrow */
    
} 

.tab-content{
    background: #fdfdfd;
    line-height: 25px;
    border: 1px solid #ddd;
    border-top:5px solid var(--primary);
    border-bottom:5px solid var(--primary);
    padding:30px 25px;
}

nav > div a.nav-item.nav-link:hover,
nav > div a.nav-item.nav-link:focus
{
    border: none;
    background: var(--primary);
    color:#fff;
    border-radius:0;
    transition:background 0.20s linear;
}

nav > div a.nav-item.nav-link.active
{
    background: var(--primary);
}

nav > div a.nav-item.nav-link:hover {
    color: var(--primary);
    background-color: #333;
}

.order-table-container {
    overflow-x: auto;
}
/* templates/master_admin/orders.html ended */

/* Pie chart on index.html START */
.txt-word {
    color: #4e73df;
}

.txt-pdf {
    color: #1cc88a;
}

.txt-ai {
    color: #36b9cc;
}

.txt-legal {
    color: #f6c23e;
}

.txt-service {
    color: #b197fc;
}

.txt-service-quote {
    color: #e74a3b;
}
/* Pie chart on index.html END */

/* on search_results.html START */
#term-span {
    color: var(--primary);
}
/* on search_results.html END */

/* search_dropdown.html START */
.st_dropdown {
    position: absolute;
    /* top: 100%; */
    /* left: 0; */
    z-index: 1000;
    display: none;
    float: left;
    min-width: 160px;
    padding: 5px 0;
    margin: 2px 0 0;
    font-size: 14px;
    text-align: left;
    list-style: none;
    background-color: #fff;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
    border: 1px solid #ccc;
    border: 1px solid rgba(0, 0, 0, .15);
    border-radius: 4px;
    -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, .175);
    box-shadow: 0 6px 12px rgba(0, 0, 0, .175);
}

#stock_dropdown {
    display: none;
    /* padding: 10px 0 0 0; */
    /* margin: 0 0 0 286px; */
    /* width: 50%; */
    
    max-height: 400%;
    overflow: auto;
}


#stock_dropdown li {
    display: inline-block;
    width: 100%;
    margin-top: 5px;
    font-size: 10px;
    text-align: left;
    padding: 0.1em 1em;
}

#search_dropdown2 li {
    display: inline-block;
    width: 100%;
    margin-top: 5px;
    font-size: 10px;
    text-align: left;
    padding: 0.1em 1em;
}


@media only screen and (max-width: 600px) {
    #stock_dropdown {  /* latest */
        display: none;
        /*
        padding: 0;
        margin: 0 0 0 -8px;
        width: 100%; */
        max-height: 400%;
        overflow: auto;
    }

    #search_dropdown2 {
      display: block; 
      /* padding: 0; */
      /* margin: 0 0 0 -8px; */
      /* width: 100%; */
      width: auto; /* fit-content */; top: 77%; left: 4%; right: 4%; /* added */
      max-height: 400%;
      overflow: auto;
    }

}

.s-link {
    font-size: .85rem;
}

.s-list-item:hover {
    background-color: var(--primary) !important;
    cursor: pointer;
}

.s-list-item:hover .s-link {
    color: #000;
}


.highlighted-term {
    /* font-weight: bold; */
    background-color: #e6e9eb;
    padding-left: 2px;
    padding-right: 2px;
}

.s-list-item:hover .highlighted-term {
    background-color: #000;
    color: var(--primary) !important;
}

/* search_dropdown.html END */