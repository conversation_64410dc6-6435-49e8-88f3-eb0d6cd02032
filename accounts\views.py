import json
import threading
import urllib
# import pyrebase  # Temporarily commented out due to compatibility issues

from django.shortcuts import render, redirect
from django.conf import settings
from django.contrib import messages
from django.contrib.auth.forms import PasswordChangeForm
from django.contrib.auth.decorators import login_required
from django.contrib.auth.views import LoginView
from django.contrib.auth import update_session_auth_hash
from django.contrib.auth.forms import PasswordChangeForm
from django.contrib.auth import update_session_auth_hash
from django.contrib.auth import logout
from django.http import HttpResponseRedirect
from django.urls import reverse, reverse_lazy

from app.common import CommonMixin
from app.models import CountryMaster, StateMaster, SubscribeNewsletterForm
from email_configuration.views import send_admin_email_for_signup, send_verification_email, send_welcome_mail
from email_configuration.models import Contact, UserOtpToken
from user_profile.models import Profile
from .forms import <PERSON>reate<PERSON><PERSON><PERSON>orm, CustomAuthenticationForm
from django.utils import timezone
from django.contrib.auth.models import User
from django.template import Context
from django.template import Template

from django.contrib.auth.forms import PasswordResetForm
from django.contrib.auth.views import PasswordResetView
from email_configuration.views import send_password_reset_confirm_email
from django.contrib.auth.tokens import default_token_generator
from django.contrib.sites.shortcuts import get_current_site
from django.utils.encoding import force_bytes
from django.utils.http import urlsafe_base64_encode
from sample.settings import DEFAULT_FROM_EMAIL as ADMIN_EMAIL

# Create your views here.

config = {
  "apiKey": "AIzaSyDl9op_I5VZP40rOey9kQrTUFTEx8bySdA",
  "authDomain": "contract-easily.firebaseapp.com",
  "projectId" : "contract-easily",
  "databaseURL": "https://contract-easily-default-rtdb.firebaseio.com/",
  "storageBucket": "contract-easily.appspot.com",
  "messagingSenderId": "627096086779",
  "appId": "1:627096086779:web:b75196b9e9d6a71ffda85e",
  "measurementId": "G-X4WZ662EJV"
}

# firebase = pyrebase.initialize_app(config)  # Temporarily commented out
# authe = firebase.auth()  # Temporarily commented out



def render_template_string(template_string, context_dict):
	"""
	Renders a string as a Django template using the provided context dictionary.
	"""
	template = Template(template_string)
	context = Context(context_dict)
	return template.render(context)



def send_email_for_signUp(user, otp, email, verify_email_complete_url, profile_object, url_signUp):
	# send email to customer
	send_verification_email(user, otp, email, verify_email_complete_url)
	# send email to admin
	send_admin_email_for_signup(profile_object, ADMIN_EMAIL, 'ADMIN-NEW-USER-SIGNUP-MAIL', url_signUp)


def signup_view(request):
	"""
	Signup View
	"""
	if request.method == 'POST':
		form = CreateUserForm(request.POST)
		if form.is_valid():
			username = form.cleaned_data.get('username')
			email = form.cleaned_data.get('email')
			password = form.cleaned_data.get('password1')

			# Begin reCAPTCHA validation
			recaptcha_response = request.POST.get('g-recaptcha-response')
			url = 'https://www.google.com/recaptcha/api/siteverify'
			values = {
				'secret': settings.GOOGLE_RECAPTCHA_SECRET_KEY,
				'response': recaptcha_response
			}
			data = urllib.parse.urlencode(values).encode()
			req =  urllib.request.Request(url, data=data)
			response = urllib.request.urlopen(req)
			result = json.loads(response.read().decode())
			# End reCAPTCHA validation

			if result['success']:
			
				form.save()

				# authe_user = authe.create_user_with_email_and_password(email, password)  # commented this
				# email_verify = authe.send_email_verification(authe_user['idToken'])

				# send_welcome_mail(user=username,to_mail=email) # cut
				
				# print(authe.get_account_info(authe_user['idToken']))  

				news_letter = SubscribeNewsletterForm.objects.create(email=email)
				news_letter.save()
				# messages.success(     # cut
				#     request, 'Account created successfully for {0}'.format(username))
				# return redirect('accounts:login')  # cut
				
				user = User.objects.filter(username=username, email=email).first()
				
				if user.is_superuser:
					pass
				else:
					UserOtpToken.objects.create(user=user, otp_expires_at=timezone.now() + timezone.timedelta(minutes=5))
					user.is_active=False 
					user.save()
				
				# contact = Contact.objects.create(username=user.username, email=user.email, source="signup")
				# contact.first_name = user.first_name
				# contact.last_name = user.last_name
				
				profile = Profile.objects.filter(email=email).first()
				profile.full_name = user.first_name + " " + user.last_name
				country_value = request.POST.get('country', '')
				if country_value:
					country_obj = CountryMaster.objects.filter(country_name=country_value).first()
					profile.country = country_obj
				# 	contact.country = country_value
				
				state_value = request.POST.get('state', '')
				if state_value:
					state_obj = StateMaster.objects.filter(state_name=state_value).first()
					profile.state = state_obj
				# 	contact.state = state_value
				
				city_value = request.POST.get('city', '')
				if city_value:
					profile.city = city_value
				# 	contact.city = city_value
				
				mobile_value = request.POST.get('mobile', '')
				if mobile_value:
					profile.phone_no = mobile_value
				# 	contact.phone_number = mobile_value
				
				profile.save()
				# contact.save()
				
				otp = UserOtpToken.objects.filter(user=user).last()

				verify_email_url = reverse('accounts:verify_email', args=[username])
				verify_email_complete_url = request.build_absolute_uri(verify_email_url)
				
				# send_verification_email(user, otp, email, verify_email_complete_url)
				url_signUp = request.scheme + "://" + request.get_host() +  reverse('accounts:signup')
				thread = threading.Thread(target=send_email_for_signUp, args=(user, otp, email, verify_email_complete_url, profile, url_signUp))
				thread.start()

				messages.success(request, "Account created successfully! An OTP was sent to your Email")
				return redirect("accounts:verify_email", username=username)
			else:
				messages.error(request, 'Invalid reCAPTCHA. Please try again.')
				return HttpResponseRedirect(request.META.get("HTTP_REFERER"))
			
		else:
			messages.error(request, 'Please Correct The Error Below.')
	else:
		form = CreateUserForm()
	countries = CountryMaster.objects.all()
	states = StateMaster.objects.all()
	
	context = {
		'form': form,
		'countries': countries,
		'states': states
	}

	return CommonMixin.render(request, 'signup.html', context)


class CustomLoginView(CommonMixin, LoginView):
	"""
	Custom Auth Login View To pass extra context
	"""
	template_name = "login.html"
	form_class = CustomAuthenticationForm



@login_required
def change_password_view(request):
	"""
	Change Password View
	"""
	profile = Profile.objects.filter(user=request.user).first()

	if request.method == 'POST':
		form = PasswordChangeForm(request.user, request.POST)
		if form.is_valid():
			user = form.save()
			update_session_auth_hash(request, user)  # Important!
			messages.success(
				request, 'Your password was successfully updated!')
			return redirect('user_profile:profile_details')

		messages.error(request, 'Please correct the error below.')
	else:
		form = PasswordChangeForm(request.user)

	# print(authe.get_account_info(user['pTzGjcM02eenoPysSvNMXmgiVuy1']))

	context = {
		'form': form,
		'profile' : profile,
	}

	return render(request, 'change_password.html', context)


def web_logout_view(request):
	logout(request)
	return redirect('index')





def verify_email(request, username):
	user = User.objects.get(username=username)
	user_otp = UserOtpToken.objects.filter(user=user).last()
	
	
	if request.method == 'POST':
		# valid token
		if user_otp.otp_code == request.POST['otp_code']:
			
			# checking for expired token
			if user_otp.otp_expires_at > timezone.now():
				user.is_active=True
				user.save()
				# Save data in Contact model
				messages.success(request, "Account activated successfully!! You can Login.")
				send_welcome_mail(user=user.username, to_mail=user.email)
				return redirect("accounts:login")
			
			# expired token
			else:
				messages.error(request, "The OTP has expired, get a new OTP!")
				return redirect("accounts:verify_email", username=user.username)
		
		
		# invalid otp code
		else:
			messages.error(request, "Invalid OTP entered, enter a valid OTP!")
			return redirect("accounts:verify_email", username=user.username)
		
	context = {}
	return render(request, "verify_email/verify_token.html", context)



def resend_otp(request):
	if request.method == 'POST':
		user_email = request.POST["otp_email"]
		
		if User.objects.filter(email=user_email).exists():
			user = User.objects.get(email=user_email)
			otp = UserOtpToken.objects.create(user=user, otp_expires_at=timezone.now() + timezone.timedelta(minutes=5))
			
			verify_email_url = reverse('accounts:verify_email', args=[user.username])
			verify_email_complete_url = request.build_absolute_uri(verify_email_url)
			send_verification_email(user, otp, user_email, verify_email_complete_url)
			
			messages.success(request, "A new OTP has been sent to your email.")
			return redirect("accounts:verify_email", username=user.username)

		else:
			messages.error(request, "This email does not exist.")
			return redirect("accounts:resend_otp")

	context = {}
	return render(request, "verify_email/resend_otp.html", context)



class MyPasswordResetForm(PasswordResetForm):
 
	def __init__(self, *args, **kwargs):
		self.request = kwargs.pop('request', None)
		super().__init__(*args, **kwargs)

	def send_mail(self, subject_template_name, email_template_name,
				  context, from_email, to_email, html_email_template_name=None):
		"""
		Send a django.core.mail.EmailMultiAlternatives to `to_email`.
		"""
		email = self.cleaned_data["email"]
		user = User.objects.get(email=email)
		uid = context['uid']
		token = context['token']
		
		password_reset_confirm_url = reverse('accounts:password_reset_confirm', args=[uid, token])
		password_reset_confirm_absolute_url = self.request.build_absolute_uri(password_reset_confirm_url)
		context['password_reset_confirm_url']= password_reset_confirm_absolute_url
		send_password_reset_confirm_email(context, from_email, to_email, email_template_name)
		
  
	
	def save(self, domain_override=None,
			 subject_template_name=None,
			 email_template_name='password_reset/password_reset_email.html',
			 use_https=False, token_generator=default_token_generator,
			 from_email=None, request=None, html_email_template_name=None,
			 extra_email_context=None):
		"""
		Generate a one-use only link for resetting password and send it to the
		user.
		"""
		
		email = self.cleaned_data["email"]
		email_field_name = User.get_email_field_name()
		for user in self.get_users(email):
			if not domain_override:
				current_site = get_current_site(request)
				site_name = current_site.name
				domain = current_site.domain
			else:
				site_name = domain = domain_override
			user_email = getattr(user, email_field_name)
			context = {
				'email': user_email,
				'domain': domain,
				'site_name': site_name,
				'uid': urlsafe_base64_encode(force_bytes(user.pk)),
				'user': user,
				'token': token_generator.make_token(user),
				'protocol': 'https' if use_https else 'http',
				**(extra_email_context or {}),
			}
			self.send_mail(
				subject_template_name, email_template_name, context, from_email,
				user_email, html_email_template_name=html_email_template_name,
			)
  
		

class CustomPasswordResetView(PasswordResetView):
	email_template_name = 'password_reset/password_reset_email.html'
	template_name = 'password_reset/password_reset_form.html'
	form_class = MyPasswordResetForm
	success_url = reverse_lazy('accounts:password_reset_done')
 
	def get_form_kwargs(self):
		"""
		Returns the keyword arguments for instantiating the form.
		"""
		kwargs = super().get_form_kwargs()
		kwargs['request'] = self.request  # Pass the request object to the form
		return kwargs
 
	
  
	def form_valid(self, form):
		email = form.cleaned_data.get('email')
		if not User.objects.filter(email=email).exists():
			messages.error(self.request, "This email does not exist.")
			return redirect("accounts:password_reset")
		return super().form_valid(form)
		
