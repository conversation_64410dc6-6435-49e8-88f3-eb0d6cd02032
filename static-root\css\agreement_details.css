@media (max-width: 991px) {
    .make-btn {
        margin-right: auto;
        margin-left: auto;
    }

    .make-with-ai-btn, .make-with-legal-help-btn {
        /* padding: 10px;  */
        white-space: normal;

    }

    .make-with-legal-help-btn {
        margin-top: 60px;
    }

    .search-div {
        padding-left: 15px;
        padding-right: 15px;
    }
}


.asset-button button, .asset-button-2 button, .asset-button-3 button {
    font-size: 20px;
}


.asset-button-2 button {
    /* color: #333333!important;  */
    color: #fff !important;
    /* added */
    font-family: 'Open Sans', sans-serif;
    font-weight: bold;
    padding: 17px 26px;
    /* border: 2px solid #eee!important;  */
    border: 2px solid #d5aa6d !important;
    /* added */
    /*background: #fff!important; */
    background: #d5aa6d !important;
    /* added */
    line-height: 1;
    border-radius: 0;
    outline: 0;
    box-shadow: none;
    -webkit-transition: all 300ms linear 0ms;
    -o-transition: all 300ms linear 0ms;
    -mz-transition: all 300ms linear 0ms;
    transition: all 300ms linear 0ms;
}

.asset-button-2 button:hover,
.asset-button-2 button:active,
/* .asset-button-2 button:focus,  */
.asset-button-2 button:visited {
    outline: none;
    text-decoration: none;
    color: #333333 !important;
    background-color: #fff !important;
    border-color: #eee !important;
    -webkit-transition: all 300ms linear 0ms;
    -o-transition: all 300ms linear 0ms;
    -mz-transition: all 300ms linear 0ms;
    transition: all 300ms linear 0ms;
    box-shadow: none;
}

.asset-button-3 button {
    /* color: #333333!important;  */
    color: #d5aa6d !important;
    /* added */
    font-family: 'Open Sans', sans-serif;
    font-weight: bold;
    padding: 17px 26px;
    /* border: 2px solid #eee!important;  */
    border: 2px solid #000 !important;
    /* added */
    /*background: #fff!important; */
    background: #000 !important;
    /* added */
    line-height: 1;
    border-radius: 0;
    outline: 0;
    box-shadow: none;
    -webkit-transition: all 300ms linear 0ms;
    -o-transition: all 300ms linear 0ms;
    -mz-transition: all 300ms linear 0ms;
    transition: all 300ms linear 0ms;
}

.asset-button-3 button:hover,
.asset-button-3 button:active,
.asset-button-3 button:focus,
.asset-button-3 button:visited {
    outline: none;
    text-decoration: none;
    color: #000 !important;
    background-color: #d5aa6d !important;
    border-color: #d5aa6d !important;
    -webkit-transition: all 300ms linear 0ms;
    -o-transition: all 300ms linear 0ms;
    -mz-transition: all 300ms linear 0ms;
    transition: all 300ms linear 0ms;
    box-shadow: none;
}


/*  ========= disable right click menu  */
.content-div {
    /* Prevent right-click context menu */
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.search-div {
    margin-bottom: 60px;
}

#aggrement_input {
    height: 60px;
}


.input_field {
    position: relative;
    height: 45px;
    margin-top: 15px;
    width: 100%;
}

.refresh_button {
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
    background: #d5aa6d;
    /* #826afb;  */
    height: 30px;
    width: 30px;
    border: none;
    border-radius: 4px;
    color: #fff;
    cursor: pointer;
}

.refresh_button:active {
    transform: translateY(-50%) scale(0.98);
}

.input_field input,
.submit_button button {
    height: 100%;
    width: 100%;
    outline: none;
    border: none;
    border-radius: 8px;
}

.input_field input {
    padding: 0 15px;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.captch_box input {
    color: #6b6b6b;
    font-size: 22px;
    pointer-events: none;
}

.captch_input input:focus {
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.08);
}

.message {
    /* font-size: 14px;  */
    margin: 14px 0;
    /* color: #826afb;  */
    display: none;
}

.message.active {
    display: block;
}

.submit_button button {
    background: #d5aa6d;
    /* #826afb;  */
    color: #fff;
    cursor: pointer;
    user-select: none;
}

.submit_button button:active {
    transform: scale(0.99);
}

.submit_button.disabled {
    opacity: 0.6;
    pointer-events: none;
}

.material-symbols-outlined {
    margin-left: -3px;
    margin-top: 2px;
}


.btn-custom:hover, .btn-custom:focus {
    color: #d5aa6d;
}

@media (max-width: 767px) {
    .contact-us-1-form button, .contact-us-1-form button:hover, .contact-us-1-form button:focus {
        width: initial !important; 
    }
}

.errorlist {
    color: red;
    list-style-type: none;
    padding-left: 0px;
}

.error_message {
    color: red;
}

.ask-btn {
    color: #000;
    background-color: #d5aa6d;
    border-radius: 0px;
}
.ask-btn:hover {
    color: #d5aa6d;
    background-color: #000;
}