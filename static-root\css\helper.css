/*--
    - Custom Column
------------------------------------------*/
@media only screen and (min-width: 1500px) {
  .col-xlg-1 {
    position: relative;
    min-height: 1px;
    padding-right: 15px;
    padding-left: 15px;
    -webkit-box-flex: 0;
        -ms-flex: 0 0 8.3333333333%;
            flex: 0 0 8.3333333333%;
    max-width: 8.3333333333%;
  }

  .col-xlg-2 {
    position: relative;
    min-height: 1px;
    padding-right: 15px;
    padding-left: 15px;
    -webkit-box-flex: 0;
        -ms-flex: 0 0 16.6666666667%;
            flex: 0 0 16.6666666667%;
    max-width: 16.6666666667%;
  }

  .col-xlg-3 {
    position: relative;
    min-height: 1px;
    padding-right: 15px;
    padding-left: 15px;
    -webkit-box-flex: 0;
        -ms-flex: 0 0 25%;
            flex: 0 0 25%;
    max-width: 25%;
  }

  .col-xlg-4 {
    position: relative;
    min-height: 1px;
    padding-right: 15px;
    padding-left: 15px;
    -webkit-box-flex: 0;
        -ms-flex: 0 0 33.3333333333%;
            flex: 0 0 33.3333333333%;
    max-width: 33.3333333333%;
  }

  .col-xlg-5 {
    position: relative;
    min-height: 1px;
    padding-right: 15px;
    padding-left: 15px;
    -webkit-box-flex: 0;
        -ms-flex: 0 0 41.6666666667%;
            flex: 0 0 41.6666666667%;
    max-width: 41.6666666667%;
  }

  .col-xlg-6 {
    position: relative;
    min-height: 1px;
    padding-right: 15px;
    padding-left: 15px;
    -webkit-box-flex: 0;
        -ms-flex: 0 0 50%;
            flex: 0 0 50%;
    max-width: 50%;
  }

  .col-xlg-7 {
    position: relative;
    min-height: 1px;
    padding-right: 15px;
    padding-left: 15px;
    -webkit-box-flex: 0;
        -ms-flex: 0 0 58.3333333333%;
            flex: 0 0 58.3333333333%;
    max-width: 58.3333333333%;
  }

  .col-xlg-8 {
    position: relative;
    min-height: 1px;
    padding-right: 15px;
    padding-left: 15px;
    -webkit-box-flex: 0;
        -ms-flex: 0 0 66.6666666667%;
            flex: 0 0 66.6666666667%;
    max-width: 66.6666666667%;
  }

  .col-xlg-9 {
    position: relative;
    min-height: 1px;
    padding-right: 15px;
    padding-left: 15px;
    -webkit-box-flex: 0;
        -ms-flex: 0 0 75%;
            flex: 0 0 75%;
    max-width: 75%;
  }

  .col-xlg-10 {
    position: relative;
    min-height: 1px;
    padding-right: 15px;
    padding-left: 15px;
    -webkit-box-flex: 0;
        -ms-flex: 0 0 83.3333333333%;
            flex: 0 0 83.3333333333%;
    max-width: 83.3333333333%;
  }

  .col-xlg-11 {
    position: relative;
    min-height: 1px;
    padding-right: 15px;
    padding-left: 15px;
    -webkit-box-flex: 0;
        -ms-flex: 0 0 91.6666666667%;
            flex: 0 0 91.6666666667%;
    max-width: 91.6666666667%;
  }

  .col-xlg-12 {
    position: relative;
    min-height: 1px;
    padding-right: 15px;
    padding-left: 15px;
    -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
            flex: 0 0 100%;
    max-width: 100%;
  }
}
/*--
    - Custom Row
------------------------------------------*/
.row-0 {
  margin-left: 0px;
  margin-right: 0px;
}
.row-0 > [class*=col] {
  padding-left: 0px;
  padding-right: 0px;
}

.row-1 {
  margin-left: -1px;
  margin-right: -1px;
}
.row-1 > [class*=col] {
  padding-left: 1px;
  padding-right: 1px;
}

.row-2 {
  margin-left: -2px;
  margin-right: -2px;
}
.row-2 > [class*=col] {
  padding-left: 2px;
  padding-right: 2px;
}

.row-3 {
  margin-left: -3px;
  margin-right: -3px;
}
.row-3 > [class*=col] {
  padding-left: 3px;
  padding-right: 3px;
}

.row-4 {
  margin-left: -4px;
  margin-right: -4px;
}
.row-4 > [class*=col] {
  padding-left: 4px;
  padding-right: 4px;
}

.row-5 {
  margin-left: -5px;
  margin-right: -5px;
}
.row-5 > [class*=col] {
  padding-left: 5px;
  padding-right: 5px;
}

.row-6 {
  margin-left: -6px;
  margin-right: -6px;
}
.row-6 > [class*=col] {
  padding-left: 6px;
  padding-right: 6px;
}

.row-7 {
  margin-left: -7px;
  margin-right: -7px;
}
.row-7 > [class*=col] {
  padding-left: 7px;
  padding-right: 7px;
}

.row-8 {
  margin-left: -8px;
  margin-right: -8px;
}
.row-8 > [class*=col] {
  padding-left: 8px;
  padding-right: 8px;
}

.row-9 {
  margin-left: -9px;
  margin-right: -9px;
}
.row-9 > [class*=col] {
  padding-left: 9px;
  padding-right: 9px;
}

.row-10 {
  margin-left: -10px;
  margin-right: -10px;
}
.row-10 > [class*=col] {
  padding-left: 10px;
  padding-right: 10px;
}

.row-11 {
  margin-left: -11px;
  margin-right: -11px;
}
.row-11 > [class*=col] {
  padding-left: 11px;
  padding-right: 11px;
}

.row-12 {
  margin-left: -12px;
  margin-right: -12px;
}
.row-12 > [class*=col] {
  padding-left: 12px;
  padding-right: 12px;
}

.row-13 {
  margin-left: -13px;
  margin-right: -13px;
}
.row-13 > [class*=col] {
  padding-left: 13px;
  padding-right: 13px;
}

.row-14 {
  margin-left: -14px;
  margin-right: -14px;
}
.row-14 > [class*=col] {
  padding-left: 14px;
  padding-right: 14px;
}

.row-15 {
  margin-left: -15px;
  margin-right: -15px;
}
.row-15 > [class*=col] {
  padding-left: 15px;
  padding-right: 15px;
}

.row-16 {
  margin-left: -16px;
  margin-right: -16px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-16 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-16 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-16 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-16 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
.row-16 > [class*=col] {
  padding-left: 16px;
  padding-right: 16px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-16 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-16 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-16 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-16 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}

.row-17 {
  margin-left: -17px;
  margin-right: -17px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-17 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-17 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-17 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-17 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
.row-17 > [class*=col] {
  padding-left: 17px;
  padding-right: 17px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-17 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-17 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-17 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-17 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}

.row-18 {
  margin-left: -18px;
  margin-right: -18px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-18 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-18 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-18 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-18 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
.row-18 > [class*=col] {
  padding-left: 18px;
  padding-right: 18px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-18 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-18 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-18 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-18 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}

.row-19 {
  margin-left: -19px;
  margin-right: -19px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-19 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-19 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-19 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-19 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
.row-19 > [class*=col] {
  padding-left: 19px;
  padding-right: 19px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-19 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-19 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-19 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-19 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}

.row-20 {
  margin-left: -20px;
  margin-right: -20px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-20 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-20 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-20 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-20 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
.row-20 > [class*=col] {
  padding-left: 20px;
  padding-right: 20px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-20 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-20 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-20 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-20 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}

.row-21 {
  margin-left: -21px;
  margin-right: -21px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-21 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-21 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-21 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-21 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
.row-21 > [class*=col] {
  padding-left: 21px;
  padding-right: 21px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-21 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-21 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-21 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-21 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}

.row-22 {
  margin-left: -22px;
  margin-right: -22px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-22 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-22 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-22 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-22 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
.row-22 > [class*=col] {
  padding-left: 22px;
  padding-right: 22px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-22 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-22 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-22 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-22 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}

.row-23 {
  margin-left: -23px;
  margin-right: -23px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-23 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-23 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-23 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-23 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
.row-23 > [class*=col] {
  padding-left: 23px;
  padding-right: 23px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-23 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-23 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-23 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-23 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}

.row-24 {
  margin-left: -24px;
  margin-right: -24px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-24 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-24 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-24 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-24 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
.row-24 > [class*=col] {
  padding-left: 24px;
  padding-right: 24px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-24 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-24 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-24 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-24 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}

.row-25 {
  margin-left: -25px;
  margin-right: -25px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-25 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-25 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-25 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-25 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
.row-25 > [class*=col] {
  padding-left: 25px;
  padding-right: 25px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-25 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-25 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-25 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-25 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}

.row-26 {
  margin-left: -26px;
  margin-right: -26px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-26 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-26 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-26 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-26 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
.row-26 > [class*=col] {
  padding-left: 26px;
  padding-right: 26px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-26 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-26 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-26 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-26 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}

.row-27 {
  margin-left: -27px;
  margin-right: -27px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-27 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-27 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-27 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-27 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
.row-27 > [class*=col] {
  padding-left: 27px;
  padding-right: 27px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-27 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-27 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-27 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-27 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}

.row-28 {
  margin-left: -28px;
  margin-right: -28px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-28 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-28 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-28 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-28 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
.row-28 > [class*=col] {
  padding-left: 28px;
  padding-right: 28px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-28 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-28 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-28 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-28 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}

.row-29 {
  margin-left: -29px;
  margin-right: -29px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-29 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-29 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-29 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-29 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
.row-29 > [class*=col] {
  padding-left: 29px;
  padding-right: 29px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-29 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-29 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-29 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-29 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}

.row-30 {
  margin-left: -30px;
  margin-right: -30px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-30 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-30 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-30 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-30 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
.row-30 > [class*=col] {
  padding-left: 30px;
  padding-right: 30px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-30 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-30 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-30 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-30 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}

.row-31 {
  margin-left: -31px;
  margin-right: -31px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-31 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-31 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-31 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-31 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
.row-31 > [class*=col] {
  padding-left: 31px;
  padding-right: 31px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-31 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-31 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-31 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-31 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}

.row-32 {
  margin-left: -32px;
  margin-right: -32px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-32 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-32 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-32 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-32 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
.row-32 > [class*=col] {
  padding-left: 32px;
  padding-right: 32px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-32 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-32 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-32 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-32 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}

.row-33 {
  margin-left: -33px;
  margin-right: -33px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-33 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-33 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-33 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-33 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
.row-33 > [class*=col] {
  padding-left: 33px;
  padding-right: 33px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-33 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-33 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-33 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-33 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}

.row-34 {
  margin-left: -34px;
  margin-right: -34px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-34 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-34 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-34 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-34 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
.row-34 > [class*=col] {
  padding-left: 34px;
  padding-right: 34px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-34 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-34 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-34 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-34 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}

.row-35 {
  margin-left: -35px;
  margin-right: -35px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-35 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-35 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-35 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-35 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
.row-35 > [class*=col] {
  padding-left: 35px;
  padding-right: 35px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-35 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-35 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-35 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-35 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}

.row-36 {
  margin-left: -36px;
  margin-right: -36px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-36 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-36 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-36 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-36 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
.row-36 > [class*=col] {
  padding-left: 36px;
  padding-right: 36px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-36 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-36 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-36 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-36 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}

.row-37 {
  margin-left: -37px;
  margin-right: -37px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-37 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-37 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-37 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-37 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
.row-37 > [class*=col] {
  padding-left: 37px;
  padding-right: 37px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-37 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-37 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-37 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-37 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}

.row-38 {
  margin-left: -38px;
  margin-right: -38px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-38 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-38 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-38 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-38 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
.row-38 > [class*=col] {
  padding-left: 38px;
  padding-right: 38px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-38 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-38 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-38 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-38 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}

.row-39 {
  margin-left: -39px;
  margin-right: -39px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-39 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-39 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-39 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-39 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
.row-39 > [class*=col] {
  padding-left: 39px;
  padding-right: 39px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-39 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-39 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-39 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-39 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}

.row-40 {
  margin-left: -40px;
  margin-right: -40px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-40 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-40 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-40 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-40 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
.row-40 > [class*=col] {
  padding-left: 40px;
  padding-right: 40px;
}
@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .row-40 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row-40 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row-40 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (max-width: 767px) {
  .row-40 > [class*=col] {
    padding-left: 15px;
    padding-right: 15px;
  }
}

/*-- 
    - Margin & Padding
------------------------------------------------------*/
/*-- Margin --*/
.mt-0 {
  margin-top: 0px !important;
}

.mb-0 {
  margin-bottom: 0px !important;
}

.ml-0 {
  margin-left: 0px !important;
}

.mr-0 {
  margin-right: 0px !important;
}

.mtn-0 {
  margin-top: 0px !important;
}

.mbn-0 {
  margin-bottom: 0px !important;
}

.mln-0 {
  margin-left: 0px !important;
}

.mrn-0 {
  margin-right: 0px !important;
}

.mt-5 {
  margin-top: 5px !important;
}

.mb-5 {
  margin-bottom: 5px !important;
}

.ml-5 {
  margin-left: 5px !important;
}

.mr-5 {
  margin-right: 5px !important;
}

.mtn-5 {
  margin-top: -5px !important;
}

.mbn-5 {
  margin-bottom: -5px !important;
}

.mln-5 {
  margin-left: -5px !important;
}

.mrn-5 {
  margin-right: -5px !important;
}

.mt-10 {
  margin-top: 10px !important;
}

.mb-10 {
  margin-bottom: 10px !important;
}

.ml-10 {
  margin-left: 10px !important;
}

.mr-10 {
  margin-right: 10px !important;
}

.mtn-10 {
  margin-top: -10px !important;
}

.mbn-10 {
  margin-bottom: -10px !important;
}

.mln-10 {
  margin-left: -10px !important;
}

.mrn-10 {
  margin-right: -10px !important;
}

.mt-15 {
  margin-top: 15px !important;
}

.mb-15 {
  margin-bottom: 15px !important;
}

.ml-15 {
  margin-left: 15px !important;
}

.mr-15 {
  margin-right: 15px !important;
}

.mtn-15 {
  margin-top: -15px !important;
}

.mbn-15 {
  margin-bottom: -15px !important;
}

.mln-15 {
  margin-left: -15px !important;
}

.mrn-15 {
  margin-right: -15px !important;
}

.mt-20 {
  margin-top: 20px !important;
}

.mb-20 {
  margin-bottom: 20px !important;
}

.ml-20 {
  margin-left: 20px !important;
}

.mr-20 {
  margin-right: 20px !important;
}

.mtn-20 {
  margin-top: -20px !important;
}

.mbn-20 {
  margin-bottom: -20px !important;
}

.mln-20 {
  margin-left: -20px !important;
}

.mrn-20 {
  margin-right: -20px !important;
}

.mt-25 {
  margin-top: 25px !important;
}

.mb-25 {
  margin-bottom: 25px !important;
}

.ml-25 {
  margin-left: 25px !important;
}

.mr-25 {
  margin-right: 25px !important;
}

.mtn-25 {
  margin-top: -25px !important;
}

.mbn-25 {
  margin-bottom: -25px !important;
}

.mln-25 {
  margin-left: -25px !important;
}

.mrn-25 {
  margin-right: -25px !important;
}

.mt-30 {
  margin-top: 30px !important;
}

.mb-30 {
  margin-bottom: 30px !important;
}

.ml-30 {
  margin-left: 30px !important;
}

.mr-30 {
  margin-right: 30px !important;
}

.mtn-30 {
  margin-top: -30px !important;
}

.mbn-30 {
  margin-bottom: -30px !important;
}

.mln-30 {
  margin-left: -30px !important;
}

.mrn-30 {
  margin-right: -30px !important;
}

.mt-35 {
  margin-top: 35px !important;
}

.mb-35 {
  margin-bottom: 35px !important;
}

.ml-35 {
  margin-left: 35px !important;
}

.mr-35 {
  margin-right: 35px !important;
}

.mtn-35 {
  margin-top: -35px !important;
}

.mbn-35 {
  margin-bottom: -35px !important;
}

.mln-35 {
  margin-left: -35px !important;
}

.mrn-35 {
  margin-right: -35px !important;
}

.mt-40 {
  margin-top: 40px !important;
}

.mb-40 {
  margin-bottom: 40px !important;
}

.ml-40 {
  margin-left: 40px !important;
}

.mr-40 {
  margin-right: 40px !important;
}

.mtn-40 {
  margin-top: -40px !important;
}

.mbn-40 {
  margin-bottom: -40px !important;
}

.mln-40 {
  margin-left: -40px !important;
}

.mrn-40 {
  margin-right: -40px !important;
}

.mt-45 {
  margin-top: 45px !important;
}

.mb-45 {
  margin-bottom: 45px !important;
}

.ml-45 {
  margin-left: 45px !important;
}

.mr-45 {
  margin-right: 45px !important;
}

.mtn-45 {
  margin-top: -45px !important;
}

.mbn-45 {
  margin-bottom: -45px !important;
}

.mln-45 {
  margin-left: -45px !important;
}

.mrn-45 {
  margin-right: -45px !important;
}

.mt-50 {
  margin-top: 50px !important;
}

.mb-50 {
  margin-bottom: 50px !important;
}

.ml-50 {
  margin-left: 50px !important;
}

.mr-50 {
  margin-right: 50px !important;
}

.mtn-50 {
  margin-top: -50px !important;
}

.mbn-50 {
  margin-bottom: -50px !important;
}

.mln-50 {
  margin-left: -50px !important;
}

.mrn-50 {
  margin-right: -50px !important;
}

.mt-55 {
  margin-top: 55px !important;
}

.mb-55 {
  margin-bottom: 55px !important;
}

.ml-55 {
  margin-left: 55px !important;
}

.mr-55 {
  margin-right: 55px !important;
}

.mtn-55 {
  margin-top: -55px !important;
}

.mbn-55 {
  margin-bottom: -55px !important;
}

.mln-55 {
  margin-left: -55px !important;
}

.mrn-55 {
  margin-right: -55px !important;
}

.mt-60 {
  margin-top: 60px !important;
}

.mb-60 {
  margin-bottom: 60px !important;
}

.ml-60 {
  margin-left: 60px !important;
}

.mr-60 {
  margin-right: 60px !important;
}

.mtn-60 {
  margin-top: -60px !important;
}

.mbn-60 {
  margin-bottom: -60px !important;
}

.mln-60 {
  margin-left: -60px !important;
}

.mrn-60 {
  margin-right: -60px !important;
}

.mt-65 {
  margin-top: 65px !important;
}

.mb-65 {
  margin-bottom: 65px !important;
}

.ml-65 {
  margin-left: 65px !important;
}

.mr-65 {
  margin-right: 65px !important;
}

.mtn-65 {
  margin-top: -65px !important;
}

.mbn-65 {
  margin-bottom: -65px !important;
}

.mln-65 {
  margin-left: -65px !important;
}

.mrn-65 {
  margin-right: -65px !important;
}

.mt-70 {
  margin-top: 70px !important;
}

.mb-70 {
  margin-bottom: 70px !important;
}

.ml-70 {
  margin-left: 70px !important;
}

.mr-70 {
  margin-right: 70px !important;
}

.mtn-70 {
  margin-top: -70px !important;
}

.mbn-70 {
  margin-bottom: -70px !important;
}

.mln-70 {
  margin-left: -70px !important;
}

.mrn-70 {
  margin-right: -70px !important;
}

.mt-75 {
  margin-top: 75px !important;
}

.mb-75 {
  margin-bottom: 75px !important;
}

.ml-75 {
  margin-left: 75px !important;
}

.mr-75 {
  margin-right: 75px !important;
}

.mtn-75 {
  margin-top: -75px !important;
}

.mbn-75 {
  margin-bottom: -75px !important;
}

.mln-75 {
  margin-left: -75px !important;
}

.mrn-75 {
  margin-right: -75px !important;
}

.mt-80 {
  margin-top: 80px !important;
}

.mb-80 {
  margin-bottom: 80px !important;
}

.ml-80 {
  margin-left: 80px !important;
}

.mr-80 {
  margin-right: 80px !important;
}

.mtn-80 {
  margin-top: -80px !important;
}

.mbn-80 {
  margin-bottom: -80px !important;
}

.mln-80 {
  margin-left: -80px !important;
}

.mrn-80 {
  margin-right: -80px !important;
}

.mt-85 {
  margin-top: 85px !important;
}

.mb-85 {
  margin-bottom: 85px !important;
}

.ml-85 {
  margin-left: 85px !important;
}

.mr-85 {
  margin-right: 85px !important;
}

.mtn-85 {
  margin-top: -85px !important;
}

.mbn-85 {
  margin-bottom: -85px !important;
}

.mln-85 {
  margin-left: -85px !important;
}

.mrn-85 {
  margin-right: -85px !important;
}

.mt-90 {
  margin-top: 90px !important;
}

.mb-90 {
  margin-bottom: 90px !important;
}

.ml-90 {
  margin-left: 90px !important;
}

.mr-90 {
  margin-right: 90px !important;
}

.mtn-90 {
  margin-top: -90px !important;
}

.mbn-90 {
  margin-bottom: -90px !important;
}

.mln-90 {
  margin-left: -90px !important;
}

.mrn-90 {
  margin-right: -90px !important;
}

.mt-95 {
  margin-top: 95px !important;
}

.mb-95 {
  margin-bottom: 95px !important;
}

.ml-95 {
  margin-left: 95px !important;
}

.mr-95 {
  margin-right: 95px !important;
}

.mtn-95 {
  margin-top: -95px !important;
}

.mbn-95 {
  margin-bottom: -95px !important;
}

.mln-95 {
  margin-left: -95px !important;
}

.mrn-95 {
  margin-right: -95px !important;
}

.mt-100 {
  margin-top: 100px !important;
}

.mb-100 {
  margin-bottom: 100px !important;
}

.ml-100 {
  margin-left: 100px !important;
}

.mr-100 {
  margin-right: 100px !important;
}

.mtn-100 {
  margin-top: -100px !important;
}

.mbn-100 {
  margin-bottom: -100px !important;
}

.mln-100 {
  margin-left: -100px !important;
}

.mrn-100 {
  margin-right: -100px !important;
}

@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .mt-lg-0 {
    margin-top: 0px !important;
  }

  .mb-lg-0 {
    margin-bottom: 0px !important;
  }

  .ml-lg-0 {
    margin-left: 0px !important;
  }

  .mr-lg-0 {
    margin-right: 0px !important;
  }

  .mtn-lg-0 {
    margin-top: 0px !important;
  }

  .mbn-lg-0 {
    margin-bottom: 0px !important;
  }

  .mln-lg-0 {
    margin-left: 0px !important;
  }

  .mrn-lg-0 {
    margin-right: 0px !important;
  }

  .mt-lg-5 {
    margin-top: 5px !important;
  }

  .mb-lg-5 {
    margin-bottom: 5px !important;
  }

  .ml-lg-5 {
    margin-left: 5px !important;
  }

  .mr-lg-5 {
    margin-right: 5px !important;
  }

  .mtn-lg-5 {
    margin-top: -5px !important;
  }

  .mbn-lg-5 {
    margin-bottom: -5px !important;
  }

  .mln-lg-5 {
    margin-left: -5px !important;
  }

  .mrn-lg-5 {
    margin-right: -5px !important;
  }

  .mt-lg-10 {
    margin-top: 10px !important;
  }

  .mb-lg-10 {
    margin-bottom: 10px !important;
  }

  .ml-lg-10 {
    margin-left: 10px !important;
  }

  .mr-lg-10 {
    margin-right: 10px !important;
  }

  .mtn-lg-10 {
    margin-top: -10px !important;
  }

  .mbn-lg-10 {
    margin-bottom: -10px !important;
  }

  .mln-lg-10 {
    margin-left: -10px !important;
  }

  .mrn-lg-10 {
    margin-right: -10px !important;
  }

  .mt-lg-15 {
    margin-top: 15px !important;
  }

  .mb-lg-15 {
    margin-bottom: 15px !important;
  }

  .ml-lg-15 {
    margin-left: 15px !important;
  }

  .mr-lg-15 {
    margin-right: 15px !important;
  }

  .mtn-lg-15 {
    margin-top: -15px !important;
  }

  .mbn-lg-15 {
    margin-bottom: -15px !important;
  }

  .mln-lg-15 {
    margin-left: -15px !important;
  }

  .mrn-lg-15 {
    margin-right: -15px !important;
  }

  .mt-lg-20 {
    margin-top: 20px !important;
  }

  .mb-lg-20 {
    margin-bottom: 20px !important;
  }

  .ml-lg-20 {
    margin-left: 20px !important;
  }

  .mr-lg-20 {
    margin-right: 20px !important;
  }

  .mtn-lg-20 {
    margin-top: -20px !important;
  }

  .mbn-lg-20 {
    margin-bottom: -20px !important;
  }

  .mln-lg-20 {
    margin-left: -20px !important;
  }

  .mrn-lg-20 {
    margin-right: -20px !important;
  }

  .mt-lg-25 {
    margin-top: 25px !important;
  }

  .mb-lg-25 {
    margin-bottom: 25px !important;
  }

  .ml-lg-25 {
    margin-left: 25px !important;
  }

  .mr-lg-25 {
    margin-right: 25px !important;
  }

  .mtn-lg-25 {
    margin-top: -25px !important;
  }

  .mbn-lg-25 {
    margin-bottom: -25px !important;
  }

  .mln-lg-25 {
    margin-left: -25px !important;
  }

  .mrn-lg-25 {
    margin-right: -25px !important;
  }

  .mt-lg-30 {
    margin-top: 30px !important;
  }

  .mb-lg-30 {
    margin-bottom: 30px !important;
  }

  .ml-lg-30 {
    margin-left: 30px !important;
  }

  .mr-lg-30 {
    margin-right: 30px !important;
  }

  .mtn-lg-30 {
    margin-top: -30px !important;
  }

  .mbn-lg-30 {
    margin-bottom: -30px !important;
  }

  .mln-lg-30 {
    margin-left: -30px !important;
  }

  .mrn-lg-30 {
    margin-right: -30px !important;
  }

  .mt-lg-35 {
    margin-top: 35px !important;
  }

  .mb-lg-35 {
    margin-bottom: 35px !important;
  }

  .ml-lg-35 {
    margin-left: 35px !important;
  }

  .mr-lg-35 {
    margin-right: 35px !important;
  }

  .mtn-lg-35 {
    margin-top: -35px !important;
  }

  .mbn-lg-35 {
    margin-bottom: -35px !important;
  }

  .mln-lg-35 {
    margin-left: -35px !important;
  }

  .mrn-lg-35 {
    margin-right: -35px !important;
  }

  .mt-lg-40 {
    margin-top: 40px !important;
  }

  .mb-lg-40 {
    margin-bottom: 40px !important;
  }

  .ml-lg-40 {
    margin-left: 40px !important;
  }

  .mr-lg-40 {
    margin-right: 40px !important;
  }

  .mtn-lg-40 {
    margin-top: -40px !important;
  }

  .mbn-lg-40 {
    margin-bottom: -40px !important;
  }

  .mln-lg-40 {
    margin-left: -40px !important;
  }

  .mrn-lg-40 {
    margin-right: -40px !important;
  }

  .mt-lg-45 {
    margin-top: 45px !important;
  }

  .mb-lg-45 {
    margin-bottom: 45px !important;
  }

  .ml-lg-45 {
    margin-left: 45px !important;
  }

  .mr-lg-45 {
    margin-right: 45px !important;
  }

  .mtn-lg-45 {
    margin-top: -45px !important;
  }

  .mbn-lg-45 {
    margin-bottom: -45px !important;
  }

  .mln-lg-45 {
    margin-left: -45px !important;
  }

  .mrn-lg-45 {
    margin-right: -45px !important;
  }

  .mt-lg-50 {
    margin-top: 50px !important;
  }

  .mb-lg-50 {
    margin-bottom: 50px !important;
  }

  .ml-lg-50 {
    margin-left: 50px !important;
  }

  .mr-lg-50 {
    margin-right: 50px !important;
  }

  .mtn-lg-50 {
    margin-top: -50px !important;
  }

  .mbn-lg-50 {
    margin-bottom: -50px !important;
  }

  .mln-lg-50 {
    margin-left: -50px !important;
  }

  .mrn-lg-50 {
    margin-right: -50px !important;
  }

  .mt-lg-55 {
    margin-top: 55px !important;
  }

  .mb-lg-55 {
    margin-bottom: 55px !important;
  }

  .ml-lg-55 {
    margin-left: 55px !important;
  }

  .mr-lg-55 {
    margin-right: 55px !important;
  }

  .mtn-lg-55 {
    margin-top: -55px !important;
  }

  .mbn-lg-55 {
    margin-bottom: -55px !important;
  }

  .mln-lg-55 {
    margin-left: -55px !important;
  }

  .mrn-lg-55 {
    margin-right: -55px !important;
  }

  .mt-lg-60 {
    margin-top: 60px !important;
  }

  .mb-lg-60 {
    margin-bottom: 60px !important;
  }

  .ml-lg-60 {
    margin-left: 60px !important;
  }

  .mr-lg-60 {
    margin-right: 60px !important;
  }

  .mtn-lg-60 {
    margin-top: -60px !important;
  }

  .mbn-lg-60 {
    margin-bottom: -60px !important;
  }

  .mln-lg-60 {
    margin-left: -60px !important;
  }

  .mrn-lg-60 {
    margin-right: -60px !important;
  }

  .mt-lg-65 {
    margin-top: 65px !important;
  }

  .mb-lg-65 {
    margin-bottom: 65px !important;
  }

  .ml-lg-65 {
    margin-left: 65px !important;
  }

  .mr-lg-65 {
    margin-right: 65px !important;
  }

  .mtn-lg-65 {
    margin-top: -65px !important;
  }

  .mbn-lg-65 {
    margin-bottom: -65px !important;
  }

  .mln-lg-65 {
    margin-left: -65px !important;
  }

  .mrn-lg-65 {
    margin-right: -65px !important;
  }

  .mt-lg-70 {
    margin-top: 70px !important;
  }

  .mb-lg-70 {
    margin-bottom: 70px !important;
  }

  .ml-lg-70 {
    margin-left: 70px !important;
  }

  .mr-lg-70 {
    margin-right: 70px !important;
  }

  .mtn-lg-70 {
    margin-top: -70px !important;
  }

  .mbn-lg-70 {
    margin-bottom: -70px !important;
  }

  .mln-lg-70 {
    margin-left: -70px !important;
  }

  .mrn-lg-70 {
    margin-right: -70px !important;
  }

  .mt-lg-75 {
    margin-top: 75px !important;
  }

  .mb-lg-75 {
    margin-bottom: 75px !important;
  }

  .ml-lg-75 {
    margin-left: 75px !important;
  }

  .mr-lg-75 {
    margin-right: 75px !important;
  }

  .mtn-lg-75 {
    margin-top: -75px !important;
  }

  .mbn-lg-75 {
    margin-bottom: -75px !important;
  }

  .mln-lg-75 {
    margin-left: -75px !important;
  }

  .mrn-lg-75 {
    margin-right: -75px !important;
  }

  .mt-lg-80 {
    margin-top: 80px !important;
  }

  .mb-lg-80 {
    margin-bottom: 80px !important;
  }

  .ml-lg-80 {
    margin-left: 80px !important;
  }

  .mr-lg-80 {
    margin-right: 80px !important;
  }

  .mtn-lg-80 {
    margin-top: -80px !important;
  }

  .mbn-lg-80 {
    margin-bottom: -80px !important;
  }

  .mln-lg-80 {
    margin-left: -80px !important;
  }

  .mrn-lg-80 {
    margin-right: -80px !important;
  }

  .mt-lg-85 {
    margin-top: 85px !important;
  }

  .mb-lg-85 {
    margin-bottom: 85px !important;
  }

  .ml-lg-85 {
    margin-left: 85px !important;
  }

  .mr-lg-85 {
    margin-right: 85px !important;
  }

  .mtn-lg-85 {
    margin-top: -85px !important;
  }

  .mbn-lg-85 {
    margin-bottom: -85px !important;
  }

  .mln-lg-85 {
    margin-left: -85px !important;
  }

  .mrn-lg-85 {
    margin-right: -85px !important;
  }

  .mt-lg-90 {
    margin-top: 90px !important;
  }

  .mb-lg-90 {
    margin-bottom: 90px !important;
  }

  .ml-lg-90 {
    margin-left: 90px !important;
  }

  .mr-lg-90 {
    margin-right: 90px !important;
  }

  .mtn-lg-90 {
    margin-top: -90px !important;
  }

  .mbn-lg-90 {
    margin-bottom: -90px !important;
  }

  .mln-lg-90 {
    margin-left: -90px !important;
  }

  .mrn-lg-90 {
    margin-right: -90px !important;
  }

  .mt-lg-95 {
    margin-top: 95px !important;
  }

  .mb-lg-95 {
    margin-bottom: 95px !important;
  }

  .ml-lg-95 {
    margin-left: 95px !important;
  }

  .mr-lg-95 {
    margin-right: 95px !important;
  }

  .mtn-lg-95 {
    margin-top: -95px !important;
  }

  .mbn-lg-95 {
    margin-bottom: -95px !important;
  }

  .mln-lg-95 {
    margin-left: -95px !important;
  }

  .mrn-lg-95 {
    margin-right: -95px !important;
  }

  .mt-lg-100 {
    margin-top: 100px !important;
  }

  .mb-lg-100 {
    margin-bottom: 100px !important;
  }

  .ml-lg-100 {
    margin-left: 100px !important;
  }

  .mr-lg-100 {
    margin-right: 100px !important;
  }

  .mtn-lg-100 {
    margin-top: -100px !important;
  }

  .mbn-lg-100 {
    margin-bottom: -100px !important;
  }

  .mln-lg-100 {
    margin-left: -100px !important;
  }

  .mrn-lg-100 {
    margin-right: -100px !important;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .mt-md-0 {
    margin-top: 0px !important;
  }

  .mb-md-0 {
    margin-bottom: 0px !important;
  }

  .ml-md-0 {
    margin-left: 0px !important;
  }

  .mr-md-0 {
    margin-right: 0px !important;
  }

  .mtn-md-0 {
    margin-top: 0px !important;
  }

  .mbn-md-0 {
    margin-bottom: 0px !important;
  }

  .mln-md-0 {
    margin-left: 0px !important;
  }

  .mrn-md-0 {
    margin-right: 0px !important;
  }

  .mt-md-5 {
    margin-top: 5px !important;
  }

  .mb-md-5 {
    margin-bottom: 5px !important;
  }

  .ml-md-5 {
    margin-left: 5px !important;
  }

  .mr-md-5 {
    margin-right: 5px !important;
  }

  .mtn-md-5 {
    margin-top: -5px !important;
  }

  .mbn-md-5 {
    margin-bottom: -5px !important;
  }

  .mln-md-5 {
    margin-left: -5px !important;
  }

  .mrn-md-5 {
    margin-right: -5px !important;
  }

  .mt-md-10 {
    margin-top: 10px !important;
  }

  .mb-md-10 {
    margin-bottom: 10px !important;
  }

  .ml-md-10 {
    margin-left: 10px !important;
  }

  .mr-md-10 {
    margin-right: 10px !important;
  }

  .mtn-md-10 {
    margin-top: -10px !important;
  }

  .mbn-md-10 {
    margin-bottom: -10px !important;
  }

  .mln-md-10 {
    margin-left: -10px !important;
  }

  .mrn-md-10 {
    margin-right: -10px !important;
  }

  .mt-md-15 {
    margin-top: 15px !important;
  }

  .mb-md-15 {
    margin-bottom: 15px !important;
  }

  .ml-md-15 {
    margin-left: 15px !important;
  }

  .mr-md-15 {
    margin-right: 15px !important;
  }

  .mtn-md-15 {
    margin-top: -15px !important;
  }

  .mbn-md-15 {
    margin-bottom: -15px !important;
  }

  .mln-md-15 {
    margin-left: -15px !important;
  }

  .mrn-md-15 {
    margin-right: -15px !important;
  }

  .mt-md-20 {
    margin-top: 20px !important;
  }

  .mb-md-20 {
    margin-bottom: 20px !important;
  }

  .ml-md-20 {
    margin-left: 20px !important;
  }

  .mr-md-20 {
    margin-right: 20px !important;
  }

  .mtn-md-20 {
    margin-top: -20px !important;
  }

  .mbn-md-20 {
    margin-bottom: -20px !important;
  }

  .mln-md-20 {
    margin-left: -20px !important;
  }

  .mrn-md-20 {
    margin-right: -20px !important;
  }

  .mt-md-25 {
    margin-top: 25px !important;
  }

  .mb-md-25 {
    margin-bottom: 25px !important;
  }

  .ml-md-25 {
    margin-left: 25px !important;
  }

  .mr-md-25 {
    margin-right: 25px !important;
  }

  .mtn-md-25 {
    margin-top: -25px !important;
  }

  .mbn-md-25 {
    margin-bottom: -25px !important;
  }

  .mln-md-25 {
    margin-left: -25px !important;
  }

  .mrn-md-25 {
    margin-right: -25px !important;
  }

  .mt-md-30 {
    margin-top: 30px !important;
  }

  .mb-md-30 {
    margin-bottom: 30px !important;
  }

  .ml-md-30 {
    margin-left: 30px !important;
  }

  .mr-md-30 {
    margin-right: 30px !important;
  }

  .mtn-md-30 {
    margin-top: -30px !important;
  }

  .mbn-md-30 {
    margin-bottom: -30px !important;
  }

  .mln-md-30 {
    margin-left: -30px !important;
  }

  .mrn-md-30 {
    margin-right: -30px !important;
  }

  .mt-md-35 {
    margin-top: 35px !important;
  }

  .mb-md-35 {
    margin-bottom: 35px !important;
  }

  .ml-md-35 {
    margin-left: 35px !important;
  }

  .mr-md-35 {
    margin-right: 35px !important;
  }

  .mtn-md-35 {
    margin-top: -35px !important;
  }

  .mbn-md-35 {
    margin-bottom: -35px !important;
  }

  .mln-md-35 {
    margin-left: -35px !important;
  }

  .mrn-md-35 {
    margin-right: -35px !important;
  }

  .mt-md-40 {
    margin-top: 40px !important;
  }

  .mb-md-40 {
    margin-bottom: 40px !important;
  }

  .ml-md-40 {
    margin-left: 40px !important;
  }

  .mr-md-40 {
    margin-right: 40px !important;
  }

  .mtn-md-40 {
    margin-top: -40px !important;
  }

  .mbn-md-40 {
    margin-bottom: -40px !important;
  }

  .mln-md-40 {
    margin-left: -40px !important;
  }

  .mrn-md-40 {
    margin-right: -40px !important;
  }

  .mt-md-45 {
    margin-top: 45px !important;
  }

  .mb-md-45 {
    margin-bottom: 45px !important;
  }

  .ml-md-45 {
    margin-left: 45px !important;
  }

  .mr-md-45 {
    margin-right: 45px !important;
  }

  .mtn-md-45 {
    margin-top: -45px !important;
  }

  .mbn-md-45 {
    margin-bottom: -45px !important;
  }

  .mln-md-45 {
    margin-left: -45px !important;
  }

  .mrn-md-45 {
    margin-right: -45px !important;
  }

  .mt-md-50 {
    margin-top: 50px !important;
  }

  .mb-md-50 {
    margin-bottom: 50px !important;
  }

  .ml-md-50 {
    margin-left: 50px !important;
  }

  .mr-md-50 {
    margin-right: 50px !important;
  }

  .mtn-md-50 {
    margin-top: -50px !important;
  }

  .mbn-md-50 {
    margin-bottom: -50px !important;
  }

  .mln-md-50 {
    margin-left: -50px !important;
  }

  .mrn-md-50 {
    margin-right: -50px !important;
  }

  .mt-md-55 {
    margin-top: 55px !important;
  }

  .mb-md-55 {
    margin-bottom: 55px !important;
  }

  .ml-md-55 {
    margin-left: 55px !important;
  }

  .mr-md-55 {
    margin-right: 55px !important;
  }

  .mtn-md-55 {
    margin-top: -55px !important;
  }

  .mbn-md-55 {
    margin-bottom: -55px !important;
  }

  .mln-md-55 {
    margin-left: -55px !important;
  }

  .mrn-md-55 {
    margin-right: -55px !important;
  }

  .mt-md-60 {
    margin-top: 60px !important;
  }

  .mb-md-60 {
    margin-bottom: 60px !important;
  }

  .ml-md-60 {
    margin-left: 60px !important;
  }

  .mr-md-60 {
    margin-right: 60px !important;
  }

  .mtn-md-60 {
    margin-top: -60px !important;
  }

  .mbn-md-60 {
    margin-bottom: -60px !important;
  }

  .mln-md-60 {
    margin-left: -60px !important;
  }

  .mrn-md-60 {
    margin-right: -60px !important;
  }

  .mt-md-65 {
    margin-top: 65px !important;
  }

  .mb-md-65 {
    margin-bottom: 65px !important;
  }

  .ml-md-65 {
    margin-left: 65px !important;
  }

  .mr-md-65 {
    margin-right: 65px !important;
  }

  .mtn-md-65 {
    margin-top: -65px !important;
  }

  .mbn-md-65 {
    margin-bottom: -65px !important;
  }

  .mln-md-65 {
    margin-left: -65px !important;
  }

  .mrn-md-65 {
    margin-right: -65px !important;
  }

  .mt-md-70 {
    margin-top: 70px !important;
  }

  .mb-md-70 {
    margin-bottom: 70px !important;
  }

  .ml-md-70 {
    margin-left: 70px !important;
  }

  .mr-md-70 {
    margin-right: 70px !important;
  }

  .mtn-md-70 {
    margin-top: -70px !important;
  }

  .mbn-md-70 {
    margin-bottom: -70px !important;
  }

  .mln-md-70 {
    margin-left: -70px !important;
  }

  .mrn-md-70 {
    margin-right: -70px !important;
  }

  .mt-md-75 {
    margin-top: 75px !important;
  }

  .mb-md-75 {
    margin-bottom: 75px !important;
  }

  .ml-md-75 {
    margin-left: 75px !important;
  }

  .mr-md-75 {
    margin-right: 75px !important;
  }

  .mtn-md-75 {
    margin-top: -75px !important;
  }

  .mbn-md-75 {
    margin-bottom: -75px !important;
  }

  .mln-md-75 {
    margin-left: -75px !important;
  }

  .mrn-md-75 {
    margin-right: -75px !important;
  }

  .mt-md-80 {
    margin-top: 80px !important;
  }

  .mb-md-80 {
    margin-bottom: 80px !important;
  }

  .ml-md-80 {
    margin-left: 80px !important;
  }

  .mr-md-80 {
    margin-right: 80px !important;
  }

  .mtn-md-80 {
    margin-top: -80px !important;
  }

  .mbn-md-80 {
    margin-bottom: -80px !important;
  }

  .mln-md-80 {
    margin-left: -80px !important;
  }

  .mrn-md-80 {
    margin-right: -80px !important;
  }

  .mt-md-85 {
    margin-top: 85px !important;
  }

  .mb-md-85 {
    margin-bottom: 85px !important;
  }

  .ml-md-85 {
    margin-left: 85px !important;
  }

  .mr-md-85 {
    margin-right: 85px !important;
  }

  .mtn-md-85 {
    margin-top: -85px !important;
  }

  .mbn-md-85 {
    margin-bottom: -85px !important;
  }

  .mln-md-85 {
    margin-left: -85px !important;
  }

  .mrn-md-85 {
    margin-right: -85px !important;
  }

  .mt-md-90 {
    margin-top: 90px !important;
  }

  .mb-md-90 {
    margin-bottom: 90px !important;
  }

  .ml-md-90 {
    margin-left: 90px !important;
  }

  .mr-md-90 {
    margin-right: 90px !important;
  }

  .mtn-md-90 {
    margin-top: -90px !important;
  }

  .mbn-md-90 {
    margin-bottom: -90px !important;
  }

  .mln-md-90 {
    margin-left: -90px !important;
  }

  .mrn-md-90 {
    margin-right: -90px !important;
  }

  .mt-md-95 {
    margin-top: 95px !important;
  }

  .mb-md-95 {
    margin-bottom: 95px !important;
  }

  .ml-md-95 {
    margin-left: 95px !important;
  }

  .mr-md-95 {
    margin-right: 95px !important;
  }

  .mtn-md-95 {
    margin-top: -95px !important;
  }

  .mbn-md-95 {
    margin-bottom: -95px !important;
  }

  .mln-md-95 {
    margin-left: -95px !important;
  }

  .mrn-md-95 {
    margin-right: -95px !important;
  }

  .mt-md-100 {
    margin-top: 100px !important;
  }

  .mb-md-100 {
    margin-bottom: 100px !important;
  }

  .ml-md-100 {
    margin-left: 100px !important;
  }

  .mr-md-100 {
    margin-right: 100px !important;
  }

  .mtn-md-100 {
    margin-top: -100px !important;
  }

  .mbn-md-100 {
    margin-bottom: -100px !important;
  }

  .mln-md-100 {
    margin-left: -100px !important;
  }

  .mrn-md-100 {
    margin-right: -100px !important;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .mt-sm-0 {
    margin-top: 0px !important;
  }

  .mb-sm-0 {
    margin-bottom: 0px !important;
  }

  .ml-sm-0 {
    margin-left: 0px !important;
  }

  .mr-sm-0 {
    margin-right: 0px !important;
  }

  .mtn-sm-0 {
    margin-top: 0px !important;
  }

  .mbn-sm-0 {
    margin-bottom: 0px !important;
  }

  .mln-sm-0 {
    margin-left: 0px !important;
  }

  .mrn-sm-0 {
    margin-right: 0px !important;
  }

  .mt-sm-5 {
    margin-top: 5px !important;
  }

  .mb-sm-5 {
    margin-bottom: 5px !important;
  }

  .ml-sm-5 {
    margin-left: 5px !important;
  }

  .mr-sm-5 {
    margin-right: 5px !important;
  }

  .mtn-sm-5 {
    margin-top: -5px !important;
  }

  .mbn-sm-5 {
    margin-bottom: -5px !important;
  }

  .mln-sm-5 {
    margin-left: -5px !important;
  }

  .mrn-sm-5 {
    margin-right: -5px !important;
  }

  .mt-sm-10 {
    margin-top: 10px !important;
  }

  .mb-sm-10 {
    margin-bottom: 10px !important;
  }

  .ml-sm-10 {
    margin-left: 10px !important;
  }

  .mr-sm-10 {
    margin-right: 10px !important;
  }

  .mtn-sm-10 {
    margin-top: -10px !important;
  }

  .mbn-sm-10 {
    margin-bottom: -10px !important;
  }

  .mln-sm-10 {
    margin-left: -10px !important;
  }

  .mrn-sm-10 {
    margin-right: -10px !important;
  }

  .mt-sm-15 {
    margin-top: 15px !important;
  }

  .mb-sm-15 {
    margin-bottom: 15px !important;
  }

  .ml-sm-15 {
    margin-left: 15px !important;
  }

  .mr-sm-15 {
    margin-right: 15px !important;
  }

  .mtn-sm-15 {
    margin-top: -15px !important;
  }

  .mbn-sm-15 {
    margin-bottom: -15px !important;
  }

  .mln-sm-15 {
    margin-left: -15px !important;
  }

  .mrn-sm-15 {
    margin-right: -15px !important;
  }

  .mt-sm-20 {
    margin-top: 20px !important;
  }

  .mb-sm-20 {
    margin-bottom: 20px !important;
  }

  .ml-sm-20 {
    margin-left: 20px !important;
  }

  .mr-sm-20 {
    margin-right: 20px !important;
  }

  .mtn-sm-20 {
    margin-top: -20px !important;
  }

  .mbn-sm-20 {
    margin-bottom: -20px !important;
  }

  .mln-sm-20 {
    margin-left: -20px !important;
  }

  .mrn-sm-20 {
    margin-right: -20px !important;
  }

  .mt-sm-25 {
    margin-top: 25px !important;
  }

  .mb-sm-25 {
    margin-bottom: 25px !important;
  }

  .ml-sm-25 {
    margin-left: 25px !important;
  }

  .mr-sm-25 {
    margin-right: 25px !important;
  }

  .mtn-sm-25 {
    margin-top: -25px !important;
  }

  .mbn-sm-25 {
    margin-bottom: -25px !important;
  }

  .mln-sm-25 {
    margin-left: -25px !important;
  }

  .mrn-sm-25 {
    margin-right: -25px !important;
  }

  .mt-sm-30 {
    margin-top: 30px !important;
  }

  .mb-sm-30 {
    margin-bottom: 30px !important;
  }

  .ml-sm-30 {
    margin-left: 30px !important;
  }

  .mr-sm-30 {
    margin-right: 30px !important;
  }

  .mtn-sm-30 {
    margin-top: -30px !important;
  }

  .mbn-sm-30 {
    margin-bottom: -30px !important;
  }

  .mln-sm-30 {
    margin-left: -30px !important;
  }

  .mrn-sm-30 {
    margin-right: -30px !important;
  }

  .mt-sm-35 {
    margin-top: 35px !important;
  }

  .mb-sm-35 {
    margin-bottom: 35px !important;
  }

  .ml-sm-35 {
    margin-left: 35px !important;
  }

  .mr-sm-35 {
    margin-right: 35px !important;
  }

  .mtn-sm-35 {
    margin-top: -35px !important;
  }

  .mbn-sm-35 {
    margin-bottom: -35px !important;
  }

  .mln-sm-35 {
    margin-left: -35px !important;
  }

  .mrn-sm-35 {
    margin-right: -35px !important;
  }

  .mt-sm-40 {
    margin-top: 40px !important;
  }

  .mb-sm-40 {
    margin-bottom: 40px !important;
  }

  .ml-sm-40 {
    margin-left: 40px !important;
  }

  .mr-sm-40 {
    margin-right: 40px !important;
  }

  .mtn-sm-40 {
    margin-top: -40px !important;
  }

  .mbn-sm-40 {
    margin-bottom: -40px !important;
  }

  .mln-sm-40 {
    margin-left: -40px !important;
  }

  .mrn-sm-40 {
    margin-right: -40px !important;
  }

  .mt-sm-45 {
    margin-top: 45px !important;
  }

  .mb-sm-45 {
    margin-bottom: 45px !important;
  }

  .ml-sm-45 {
    margin-left: 45px !important;
  }

  .mr-sm-45 {
    margin-right: 45px !important;
  }

  .mtn-sm-45 {
    margin-top: -45px !important;
  }

  .mbn-sm-45 {
    margin-bottom: -45px !important;
  }

  .mln-sm-45 {
    margin-left: -45px !important;
  }

  .mrn-sm-45 {
    margin-right: -45px !important;
  }

  .mt-sm-50 {
    margin-top: 50px !important;
  }

  .mb-sm-50 {
    margin-bottom: 50px !important;
  }

  .ml-sm-50 {
    margin-left: 50px !important;
  }

  .mr-sm-50 {
    margin-right: 50px !important;
  }

  .mtn-sm-50 {
    margin-top: -50px !important;
  }

  .mbn-sm-50 {
    margin-bottom: -50px !important;
  }

  .mln-sm-50 {
    margin-left: -50px !important;
  }

  .mrn-sm-50 {
    margin-right: -50px !important;
  }

  .mt-sm-55 {
    margin-top: 55px !important;
  }

  .mb-sm-55 {
    margin-bottom: 55px !important;
  }

  .ml-sm-55 {
    margin-left: 55px !important;
  }

  .mr-sm-55 {
    margin-right: 55px !important;
  }

  .mtn-sm-55 {
    margin-top: -55px !important;
  }

  .mbn-sm-55 {
    margin-bottom: -55px !important;
  }

  .mln-sm-55 {
    margin-left: -55px !important;
  }

  .mrn-sm-55 {
    margin-right: -55px !important;
  }

  .mt-sm-60 {
    margin-top: 60px !important;
  }

  .mb-sm-60 {
    margin-bottom: 60px !important;
  }

  .ml-sm-60 {
    margin-left: 60px !important;
  }

  .mr-sm-60 {
    margin-right: 60px !important;
  }

  .mtn-sm-60 {
    margin-top: -60px !important;
  }

  .mbn-sm-60 {
    margin-bottom: -60px !important;
  }

  .mln-sm-60 {
    margin-left: -60px !important;
  }

  .mrn-sm-60 {
    margin-right: -60px !important;
  }

  .mt-sm-65 {
    margin-top: 65px !important;
  }

  .mb-sm-65 {
    margin-bottom: 65px !important;
  }

  .ml-sm-65 {
    margin-left: 65px !important;
  }

  .mr-sm-65 {
    margin-right: 65px !important;
  }

  .mtn-sm-65 {
    margin-top: -65px !important;
  }

  .mbn-sm-65 {
    margin-bottom: -65px !important;
  }

  .mln-sm-65 {
    margin-left: -65px !important;
  }

  .mrn-sm-65 {
    margin-right: -65px !important;
  }

  .mt-sm-70 {
    margin-top: 70px !important;
  }

  .mb-sm-70 {
    margin-bottom: 70px !important;
  }

  .ml-sm-70 {
    margin-left: 70px !important;
  }

  .mr-sm-70 {
    margin-right: 70px !important;
  }

  .mtn-sm-70 {
    margin-top: -70px !important;
  }

  .mbn-sm-70 {
    margin-bottom: -70px !important;
  }

  .mln-sm-70 {
    margin-left: -70px !important;
  }

  .mrn-sm-70 {
    margin-right: -70px !important;
  }

  .mt-sm-75 {
    margin-top: 75px !important;
  }

  .mb-sm-75 {
    margin-bottom: 75px !important;
  }

  .ml-sm-75 {
    margin-left: 75px !important;
  }

  .mr-sm-75 {
    margin-right: 75px !important;
  }

  .mtn-sm-75 {
    margin-top: -75px !important;
  }

  .mbn-sm-75 {
    margin-bottom: -75px !important;
  }

  .mln-sm-75 {
    margin-left: -75px !important;
  }

  .mrn-sm-75 {
    margin-right: -75px !important;
  }

  .mt-sm-80 {
    margin-top: 80px !important;
  }

  .mb-sm-80 {
    margin-bottom: 80px !important;
  }

  .ml-sm-80 {
    margin-left: 80px !important;
  }

  .mr-sm-80 {
    margin-right: 80px !important;
  }

  .mtn-sm-80 {
    margin-top: -80px !important;
  }

  .mbn-sm-80 {
    margin-bottom: -80px !important;
  }

  .mln-sm-80 {
    margin-left: -80px !important;
  }

  .mrn-sm-80 {
    margin-right: -80px !important;
  }

  .mt-sm-85 {
    margin-top: 85px !important;
  }

  .mb-sm-85 {
    margin-bottom: 85px !important;
  }

  .ml-sm-85 {
    margin-left: 85px !important;
  }

  .mr-sm-85 {
    margin-right: 85px !important;
  }

  .mtn-sm-85 {
    margin-top: -85px !important;
  }

  .mbn-sm-85 {
    margin-bottom: -85px !important;
  }

  .mln-sm-85 {
    margin-left: -85px !important;
  }

  .mrn-sm-85 {
    margin-right: -85px !important;
  }

  .mt-sm-90 {
    margin-top: 90px !important;
  }

  .mb-sm-90 {
    margin-bottom: 90px !important;
  }

  .ml-sm-90 {
    margin-left: 90px !important;
  }

  .mr-sm-90 {
    margin-right: 90px !important;
  }

  .mtn-sm-90 {
    margin-top: -90px !important;
  }

  .mbn-sm-90 {
    margin-bottom: -90px !important;
  }

  .mln-sm-90 {
    margin-left: -90px !important;
  }

  .mrn-sm-90 {
    margin-right: -90px !important;
  }

  .mt-sm-95 {
    margin-top: 95px !important;
  }

  .mb-sm-95 {
    margin-bottom: 95px !important;
  }

  .ml-sm-95 {
    margin-left: 95px !important;
  }

  .mr-sm-95 {
    margin-right: 95px !important;
  }

  .mtn-sm-95 {
    margin-top: -95px !important;
  }

  .mbn-sm-95 {
    margin-bottom: -95px !important;
  }

  .mln-sm-95 {
    margin-left: -95px !important;
  }

  .mrn-sm-95 {
    margin-right: -95px !important;
  }

  .mt-sm-100 {
    margin-top: 100px !important;
  }

  .mb-sm-100 {
    margin-bottom: 100px !important;
  }

  .ml-sm-100 {
    margin-left: 100px !important;
  }

  .mr-sm-100 {
    margin-right: 100px !important;
  }

  .mtn-sm-100 {
    margin-top: -100px !important;
  }

  .mbn-sm-100 {
    margin-bottom: -100px !important;
  }

  .mln-sm-100 {
    margin-left: -100px !important;
  }

  .mrn-sm-100 {
    margin-right: -100px !important;
  }
}
@media only screen and (max-width: 767px) {
  .mt-xs-0 {
    margin-top: 0px !important;
  }

  .mb-xs-0 {
    margin-bottom: 0px !important;
  }

  .ml-xs-0 {
    margin-left: 0px !important;
  }

  .mr-xs-0 {
    margin-right: 0px !important;
  }

  .mtn-xs-0 {
    margin-top: 0px !important;
  }

  .mbn-xs-0 {
    margin-bottom: 0px !important;
  }

  .mln-xs-0 {
    margin-left: 0px !important;
  }

  .mrn-xs-0 {
    margin-right: 0px !important;
  }

  .mt-xs-5 {
    margin-top: 5px !important;
  }

  .mb-xs-5 {
    margin-bottom: 5px !important;
  }

  .ml-xs-5 {
    margin-left: 5px !important;
  }

  .mr-xs-5 {
    margin-right: 5px !important;
  }

  .mtn-xs-5 {
    margin-top: -5px !important;
  }

  .mbn-xs-5 {
    margin-bottom: -5px !important;
  }

  .mln-xs-5 {
    margin-left: -5px !important;
  }

  .mrn-xs-5 {
    margin-right: -5px !important;
  }

  .mt-xs-10 {
    margin-top: 10px !important;
  }

  .mb-xs-10 {
    margin-bottom: 10px !important;
  }

  .ml-xs-10 {
    margin-left: 10px !important;
  }

  .mr-xs-10 {
    margin-right: 10px !important;
  }

  .mtn-xs-10 {
    margin-top: -10px !important;
  }

  .mbn-xs-10 {
    margin-bottom: -10px !important;
  }

  .mln-xs-10 {
    margin-left: -10px !important;
  }

  .mrn-xs-10 {
    margin-right: -10px !important;
  }

  .mt-xs-15 {
    margin-top: 15px !important;
  }

  .mb-xs-15 {
    margin-bottom: 15px !important;
  }

  .ml-xs-15 {
    margin-left: 15px !important;
  }

  .mr-xs-15 {
    margin-right: 15px !important;
  }

  .mtn-xs-15 {
    margin-top: -15px !important;
  }

  .mbn-xs-15 {
    margin-bottom: -15px !important;
  }

  .mln-xs-15 {
    margin-left: -15px !important;
  }

  .mrn-xs-15 {
    margin-right: -15px !important;
  }

  .mt-xs-20 {
    margin-top: 20px !important;
  }

  .mb-xs-20 {
    margin-bottom: 20px !important;
  }

  .ml-xs-20 {
    margin-left: 20px !important;
  }

  .mr-xs-20 {
    margin-right: 20px !important;
  }

  .mtn-xs-20 {
    margin-top: -20px !important;
  }

  .mbn-xs-20 {
    margin-bottom: -20px !important;
  }

  .mln-xs-20 {
    margin-left: -20px !important;
  }

  .mrn-xs-20 {
    margin-right: -20px !important;
  }

  .mt-xs-25 {
    margin-top: 25px !important;
  }

  .mb-xs-25 {
    margin-bottom: 25px !important;
  }

  .ml-xs-25 {
    margin-left: 25px !important;
  }

  .mr-xs-25 {
    margin-right: 25px !important;
  }

  .mtn-xs-25 {
    margin-top: -25px !important;
  }

  .mbn-xs-25 {
    margin-bottom: -25px !important;
  }

  .mln-xs-25 {
    margin-left: -25px !important;
  }

  .mrn-xs-25 {
    margin-right: -25px !important;
  }

  .mt-xs-30 {
    margin-top: 30px !important;
  }

  .mb-xs-30 {
    margin-bottom: 30px !important;
  }

  .ml-xs-30 {
    margin-left: 30px !important;
  }

  .mr-xs-30 {
    margin-right: 30px !important;
  }

  .mtn-xs-30 {
    margin-top: -30px !important;
  }

  .mbn-xs-30 {
    margin-bottom: -30px !important;
  }

  .mln-xs-30 {
    margin-left: -30px !important;
  }

  .mrn-xs-30 {
    margin-right: -30px !important;
  }

  .mt-xs-35 {
    margin-top: 35px !important;
  }

  .mb-xs-35 {
    margin-bottom: 35px !important;
  }

  .ml-xs-35 {
    margin-left: 35px !important;
  }

  .mr-xs-35 {
    margin-right: 35px !important;
  }

  .mtn-xs-35 {
    margin-top: -35px !important;
  }

  .mbn-xs-35 {
    margin-bottom: -35px !important;
  }

  .mln-xs-35 {
    margin-left: -35px !important;
  }

  .mrn-xs-35 {
    margin-right: -35px !important;
  }

  .mt-xs-40 {
    margin-top: 40px !important;
  }

  .mb-xs-40 {
    margin-bottom: 40px !important;
  }

  .ml-xs-40 {
    margin-left: 40px !important;
  }

  .mr-xs-40 {
    margin-right: 40px !important;
  }

  .mtn-xs-40 {
    margin-top: -40px !important;
  }

  .mbn-xs-40 {
    margin-bottom: -40px !important;
  }

  .mln-xs-40 {
    margin-left: -40px !important;
  }

  .mrn-xs-40 {
    margin-right: -40px !important;
  }

  .mt-xs-45 {
    margin-top: 45px !important;
  }

  .mb-xs-45 {
    margin-bottom: 45px !important;
  }

  .ml-xs-45 {
    margin-left: 45px !important;
  }

  .mr-xs-45 {
    margin-right: 45px !important;
  }

  .mtn-xs-45 {
    margin-top: -45px !important;
  }

  .mbn-xs-45 {
    margin-bottom: -45px !important;
  }

  .mln-xs-45 {
    margin-left: -45px !important;
  }

  .mrn-xs-45 {
    margin-right: -45px !important;
  }

  .mt-xs-50 {
    margin-top: 50px !important;
  }

  .mb-xs-50 {
    margin-bottom: 50px !important;
  }

  .ml-xs-50 {
    margin-left: 50px !important;
  }

  .mr-xs-50 {
    margin-right: 50px !important;
  }

  .mtn-xs-50 {
    margin-top: -50px !important;
  }

  .mbn-xs-50 {
    margin-bottom: -50px !important;
  }

  .mln-xs-50 {
    margin-left: -50px !important;
  }

  .mrn-xs-50 {
    margin-right: -50px !important;
  }

  .mt-xs-55 {
    margin-top: 55px !important;
  }

  .mb-xs-55 {
    margin-bottom: 55px !important;
  }

  .ml-xs-55 {
    margin-left: 55px !important;
  }

  .mr-xs-55 {
    margin-right: 55px !important;
  }

  .mtn-xs-55 {
    margin-top: -55px !important;
  }

  .mbn-xs-55 {
    margin-bottom: -55px !important;
  }

  .mln-xs-55 {
    margin-left: -55px !important;
  }

  .mrn-xs-55 {
    margin-right: -55px !important;
  }

  .mt-xs-60 {
    margin-top: 60px !important;
  }

  .mb-xs-60 {
    margin-bottom: 60px !important;
  }

  .ml-xs-60 {
    margin-left: 60px !important;
  }

  .mr-xs-60 {
    margin-right: 60px !important;
  }

  .mtn-xs-60 {
    margin-top: -60px !important;
  }

  .mbn-xs-60 {
    margin-bottom: -60px !important;
  }

  .mln-xs-60 {
    margin-left: -60px !important;
  }

  .mrn-xs-60 {
    margin-right: -60px !important;
  }

  .mt-xs-65 {
    margin-top: 65px !important;
  }

  .mb-xs-65 {
    margin-bottom: 65px !important;
  }

  .ml-xs-65 {
    margin-left: 65px !important;
  }

  .mr-xs-65 {
    margin-right: 65px !important;
  }

  .mtn-xs-65 {
    margin-top: -65px !important;
  }

  .mbn-xs-65 {
    margin-bottom: -65px !important;
  }

  .mln-xs-65 {
    margin-left: -65px !important;
  }

  .mrn-xs-65 {
    margin-right: -65px !important;
  }

  .mt-xs-70 {
    margin-top: 70px !important;
  }

  .mb-xs-70 {
    margin-bottom: 70px !important;
  }

  .ml-xs-70 {
    margin-left: 70px !important;
  }

  .mr-xs-70 {
    margin-right: 70px !important;
  }

  .mtn-xs-70 {
    margin-top: -70px !important;
  }

  .mbn-xs-70 {
    margin-bottom: -70px !important;
  }

  .mln-xs-70 {
    margin-left: -70px !important;
  }

  .mrn-xs-70 {
    margin-right: -70px !important;
  }

  .mt-xs-75 {
    margin-top: 75px !important;
  }

  .mb-xs-75 {
    margin-bottom: 75px !important;
  }

  .ml-xs-75 {
    margin-left: 75px !important;
  }

  .mr-xs-75 {
    margin-right: 75px !important;
  }

  .mtn-xs-75 {
    margin-top: -75px !important;
  }

  .mbn-xs-75 {
    margin-bottom: -75px !important;
  }

  .mln-xs-75 {
    margin-left: -75px !important;
  }

  .mrn-xs-75 {
    margin-right: -75px !important;
  }

  .mt-xs-80 {
    margin-top: 80px !important;
  }

  .mb-xs-80 {
    margin-bottom: 80px !important;
  }

  .ml-xs-80 {
    margin-left: 80px !important;
  }

  .mr-xs-80 {
    margin-right: 80px !important;
  }

  .mtn-xs-80 {
    margin-top: -80px !important;
  }

  .mbn-xs-80 {
    margin-bottom: -80px !important;
  }

  .mln-xs-80 {
    margin-left: -80px !important;
  }

  .mrn-xs-80 {
    margin-right: -80px !important;
  }

  .mt-xs-85 {
    margin-top: 85px !important;
  }

  .mb-xs-85 {
    margin-bottom: 85px !important;
  }

  .ml-xs-85 {
    margin-left: 85px !important;
  }

  .mr-xs-85 {
    margin-right: 85px !important;
  }

  .mtn-xs-85 {
    margin-top: -85px !important;
  }

  .mbn-xs-85 {
    margin-bottom: -85px !important;
  }

  .mln-xs-85 {
    margin-left: -85px !important;
  }

  .mrn-xs-85 {
    margin-right: -85px !important;
  }

  .mt-xs-90 {
    margin-top: 90px !important;
  }

  .mb-xs-90 {
    margin-bottom: 90px !important;
  }

  .ml-xs-90 {
    margin-left: 90px !important;
  }

  .mr-xs-90 {
    margin-right: 90px !important;
  }

  .mtn-xs-90 {
    margin-top: -90px !important;
  }

  .mbn-xs-90 {
    margin-bottom: -90px !important;
  }

  .mln-xs-90 {
    margin-left: -90px !important;
  }

  .mrn-xs-90 {
    margin-right: -90px !important;
  }

  .mt-xs-95 {
    margin-top: 95px !important;
  }

  .mb-xs-95 {
    margin-bottom: 95px !important;
  }

  .ml-xs-95 {
    margin-left: 95px !important;
  }

  .mr-xs-95 {
    margin-right: 95px !important;
  }

  .mtn-xs-95 {
    margin-top: -95px !important;
  }

  .mbn-xs-95 {
    margin-bottom: -95px !important;
  }

  .mln-xs-95 {
    margin-left: -95px !important;
  }

  .mrn-xs-95 {
    margin-right: -95px !important;
  }

  .mt-xs-100 {
    margin-top: 100px !important;
  }

  .mb-xs-100 {
    margin-bottom: 100px !important;
  }

  .ml-xs-100 {
    margin-left: 100px !important;
  }

  .mr-xs-100 {
    margin-right: 100px !important;
  }

  .mtn-xs-100 {
    margin-top: -100px !important;
  }

  .mbn-xs-100 {
    margin-bottom: -100px !important;
  }

  .mln-xs-100 {
    margin-left: -100px !important;
  }

  .mrn-xs-100 {
    margin-right: -100px !important;
  }
}
/*-- Padding --*/
.pt-0 {
  padding-top: 0px !important;
}

.pb-0 {
  padding-bottom: 0px !important;
}

.pl-0 {
  padding-left: 0px !important;
}

.pr-0 {
  padding-right: 0px !important;
}

.pt-5 {
  padding-top: 5px !important;
}

.pb-5 {
  padding-bottom: 5px !important;
}

.pl-5 {
  padding-left: 5px !important;
}

.pr-5 {
  padding-right: 5px !important;
}

.pt-10 {
  padding-top: 10px !important;
}

.pb-10 {
  padding-bottom: 10px !important;
}

.pl-10 {
  padding-left: 10px !important;
}

.pr-10 {
  padding-right: 10px !important;
}

.pt-15 {
  padding-top: 15px !important;
}

.pb-15 {
  padding-bottom: 15px !important;
}

.pl-15 {
  padding-left: 15px !important;
}

.pr-15 {
  padding-right: 15px !important;
}

.pt-20 {
  padding-top: 20px !important;
}

.pb-20 {
  padding-bottom: 20px !important;
}

.pl-20 {
  padding-left: 20px !important;
}

.pr-20 {
  padding-right: 20px !important;
}

.pt-25 {
  padding-top: 25px !important;
}

.pb-25 {
  padding-bottom: 25px !important;
}

.pl-25 {
  padding-left: 25px !important;
}

.pr-25 {
  padding-right: 25px !important;
}

.pt-30 {
  padding-top: 30px !important;
}

.pb-30 {
  padding-bottom: 30px !important;
}

.pl-30 {
  padding-left: 30px !important;
}

.pr-30 {
  padding-right: 30px !important;
}

.pt-35 {
  padding-top: 35px !important;
}

.pb-35 {
  padding-bottom: 35px !important;
}

.pl-35 {
  padding-left: 35px !important;
}

.pr-35 {
  padding-right: 35px !important;
}

.pt-40 {
  padding-top: 40px !important;
}

.pb-40 {
  padding-bottom: 40px !important;
}

.pl-40 {
  padding-left: 40px !important;
}

.pr-40 {
  padding-right: 40px !important;
}

.pt-45 {
  padding-top: 45px !important;
}

.pb-45 {
  padding-bottom: 45px !important;
}

.pl-45 {
  padding-left: 45px !important;
}

.pr-45 {
  padding-right: 45px !important;
}

.pt-50 {
  padding-top: 50px !important;
}

.pb-50 {
  padding-bottom: 50px !important;
}

.pl-50 {
  padding-left: 50px !important;
}

.pr-50 {
  padding-right: 50px !important;
}

.pt-55 {
  padding-top: 55px !important;
}

.pb-55 {
  padding-bottom: 55px !important;
}

.pl-55 {
  padding-left: 55px !important;
}

.pr-55 {
  padding-right: 55px !important;
}

.pt-60 {
  padding-top: 60px !important;
}

.pb-60 {
  padding-bottom: 60px !important;
}

.pl-60 {
  padding-left: 60px !important;
}

.pr-60 {
  padding-right: 60px !important;
}

.pt-65 {
  padding-top: 65px !important;
}

.pb-65 {
  padding-bottom: 65px !important;
}

.pl-65 {
  padding-left: 65px !important;
}

.pr-65 {
  padding-right: 65px !important;
}

.pt-70 {
  padding-top: 70px !important;
}

.pb-70 {
  padding-bottom: 70px !important;
}

.pl-70 {
  padding-left: 70px !important;
}

.pr-70 {
  padding-right: 70px !important;
}

.pt-75 {
  padding-top: 75px !important;
}

.pb-75 {
  padding-bottom: 75px !important;
}

.pl-75 {
  padding-left: 75px !important;
}

.pr-75 {
  padding-right: 75px !important;
}

.pt-80 {
  padding-top: 80px !important;
}

.pb-80 {
  padding-bottom: 80px !important;
}

.pl-80 {
  padding-left: 80px !important;
}

.pr-80 {
  padding-right: 80px !important;
}

.pt-85 {
  padding-top: 85px !important;
}

.pb-85 {
  padding-bottom: 85px !important;
}

.pl-85 {
  padding-left: 85px !important;
}

.pr-85 {
  padding-right: 85px !important;
}

.pt-90 {
  padding-top: 90px !important;
}

.pb-90 {
  padding-bottom: 90px !important;
}

.pl-90 {
  padding-left: 90px !important;
}

.pr-90 {
  padding-right: 90px !important;
}

.pt-95 {
  padding-top: 95px !important;
}

.pb-95 {
  padding-bottom: 95px !important;
}

.pl-95 {
  padding-left: 95px !important;
}

.pr-95 {
  padding-right: 95px !important;
}

.pt-100 {
  padding-top: 100px !important;
}

.pb-100 {
  padding-bottom: 100px !important;
}

.pl-100 {
  padding-left: 100px !important;
}

.pr-100 {
  padding-right: 100px !important;
}

@media only screen and (min-width: 1200px) and (max-width: 1499px) {
  .pt-lg-0 {
    padding-top: 0px !important;
  }

  .pb-lg-0 {
    padding-bottom: 0px !important;
  }

  .pl-lg-0 {
    padding-left: 0px !important;
  }

  .pr-lg-0 {
    padding-right: 0px !important;
  }

  .pt-lg-5 {
    padding-top: 5px !important;
  }

  .pb-lg-5 {
    padding-bottom: 5px !important;
  }

  .pl-lg-5 {
    padding-left: 5px !important;
  }

  .pr-lg-5 {
    padding-right: 5px !important;
  }

  .pt-lg-10 {
    padding-top: 10px !important;
  }

  .pb-lg-10 {
    padding-bottom: 10px !important;
  }

  .pl-lg-10 {
    padding-left: 10px !important;
  }

  .pr-lg-10 {
    padding-right: 10px !important;
  }

  .pt-lg-15 {
    padding-top: 15px !important;
  }

  .pb-lg-15 {
    padding-bottom: 15px !important;
  }

  .pl-lg-15 {
    padding-left: 15px !important;
  }

  .pr-lg-15 {
    padding-right: 15px !important;
  }

  .pt-lg-20 {
    padding-top: 20px !important;
  }

  .pb-lg-20 {
    padding-bottom: 20px !important;
  }

  .pl-lg-20 {
    padding-left: 20px !important;
  }

  .pr-lg-20 {
    padding-right: 20px !important;
  }

  .pt-lg-25 {
    padding-top: 25px !important;
  }

  .pb-lg-25 {
    padding-bottom: 25px !important;
  }

  .pl-lg-25 {
    padding-left: 25px !important;
  }

  .pr-lg-25 {
    padding-right: 25px !important;
  }

  .pt-lg-30 {
    padding-top: 30px !important;
  }

  .pb-lg-30 {
    padding-bottom: 30px !important;
  }

  .pl-lg-30 {
    padding-left: 30px !important;
  }

  .pr-lg-30 {
    padding-right: 30px !important;
  }

  .pt-lg-35 {
    padding-top: 35px !important;
  }

  .pb-lg-35 {
    padding-bottom: 35px !important;
  }

  .pl-lg-35 {
    padding-left: 35px !important;
  }

  .pr-lg-35 {
    padding-right: 35px !important;
  }

  .pt-lg-40 {
    padding-top: 40px !important;
  }

  .pb-lg-40 {
    padding-bottom: 40px !important;
  }

  .pl-lg-40 {
    padding-left: 40px !important;
  }

  .pr-lg-40 {
    padding-right: 40px !important;
  }

  .pt-lg-45 {
    padding-top: 45px !important;
  }

  .pb-lg-45 {
    padding-bottom: 45px !important;
  }

  .pl-lg-45 {
    padding-left: 45px !important;
  }

  .pr-lg-45 {
    padding-right: 45px !important;
  }

  .pt-lg-50 {
    padding-top: 50px !important;
  }

  .pb-lg-50 {
    padding-bottom: 50px !important;
  }

  .pl-lg-50 {
    padding-left: 50px !important;
  }

  .pr-lg-50 {
    padding-right: 50px !important;
  }

  .pt-lg-55 {
    padding-top: 55px !important;
  }

  .pb-lg-55 {
    padding-bottom: 55px !important;
  }

  .pl-lg-55 {
    padding-left: 55px !important;
  }

  .pr-lg-55 {
    padding-right: 55px !important;
  }

  .pt-lg-60 {
    padding-top: 60px !important;
  }

  .pb-lg-60 {
    padding-bottom: 60px !important;
  }

  .pl-lg-60 {
    padding-left: 60px !important;
  }

  .pr-lg-60 {
    padding-right: 60px !important;
  }

  .pt-lg-65 {
    padding-top: 65px !important;
  }

  .pb-lg-65 {
    padding-bottom: 65px !important;
  }

  .pl-lg-65 {
    padding-left: 65px !important;
  }

  .pr-lg-65 {
    padding-right: 65px !important;
  }

  .pt-lg-70 {
    padding-top: 70px !important;
  }

  .pb-lg-70 {
    padding-bottom: 70px !important;
  }

  .pl-lg-70 {
    padding-left: 70px !important;
  }

  .pr-lg-70 {
    padding-right: 70px !important;
  }

  .pt-lg-75 {
    padding-top: 75px !important;
  }

  .pb-lg-75 {
    padding-bottom: 75px !important;
  }

  .pl-lg-75 {
    padding-left: 75px !important;
  }

  .pr-lg-75 {
    padding-right: 75px !important;
  }

  .pt-lg-80 {
    padding-top: 80px !important;
  }

  .pb-lg-80 {
    padding-bottom: 80px !important;
  }

  .pl-lg-80 {
    padding-left: 80px !important;
  }

  .pr-lg-80 {
    padding-right: 80px !important;
  }

  .pt-lg-85 {
    padding-top: 85px !important;
  }

  .pb-lg-85 {
    padding-bottom: 85px !important;
  }

  .pl-lg-85 {
    padding-left: 85px !important;
  }

  .pr-lg-85 {
    padding-right: 85px !important;
  }

  .pt-lg-90 {
    padding-top: 90px !important;
  }

  .pb-lg-90 {
    padding-bottom: 90px !important;
  }

  .pl-lg-90 {
    padding-left: 90px !important;
  }

  .pr-lg-90 {
    padding-right: 90px !important;
  }

  .pt-lg-95 {
    padding-top: 95px !important;
  }

  .pb-lg-95 {
    padding-bottom: 95px !important;
  }

  .pl-lg-95 {
    padding-left: 95px !important;
  }

  .pr-lg-95 {
    padding-right: 95px !important;
  }

  .pt-lg-100 {
    padding-top: 100px !important;
  }

  .pb-lg-100 {
    padding-bottom: 100px !important;
  }

  .pl-lg-100 {
    padding-left: 100px !important;
  }

  .pr-lg-100 {
    padding-right: 100px !important;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .pt-md-0 {
    padding-top: 0px !important;
  }

  .pb-md-0 {
    padding-bottom: 0px !important;
  }

  .pl-md-0 {
    padding-left: 0px !important;
  }

  .pr-md-0 {
    padding-right: 0px !important;
  }

  .pt-md-5 {
    padding-top: 5px !important;
  }

  .pb-md-5 {
    padding-bottom: 5px !important;
  }

  .pl-md-5 {
    padding-left: 5px !important;
  }

  .pr-md-5 {
    padding-right: 5px !important;
  }

  .pt-md-10 {
    padding-top: 10px !important;
  }

  .pb-md-10 {
    padding-bottom: 10px !important;
  }

  .pl-md-10 {
    padding-left: 10px !important;
  }

  .pr-md-10 {
    padding-right: 10px !important;
  }

  .pt-md-15 {
    padding-top: 15px !important;
  }

  .pb-md-15 {
    padding-bottom: 15px !important;
  }

  .pl-md-15 {
    padding-left: 15px !important;
  }

  .pr-md-15 {
    padding-right: 15px !important;
  }

  .pt-md-20 {
    padding-top: 20px !important;
  }

  .pb-md-20 {
    padding-bottom: 20px !important;
  }

  .pl-md-20 {
    padding-left: 20px !important;
  }

  .pr-md-20 {
    padding-right: 20px !important;
  }

  .pt-md-25 {
    padding-top: 25px !important;
  }

  .pb-md-25 {
    padding-bottom: 25px !important;
  }

  .pl-md-25 {
    padding-left: 25px !important;
  }

  .pr-md-25 {
    padding-right: 25px !important;
  }

  .pt-md-30 {
    padding-top: 30px !important;
  }

  .pb-md-30 {
    padding-bottom: 30px !important;
  }

  .pl-md-30 {
    padding-left: 30px !important;
  }

  .pr-md-30 {
    padding-right: 30px !important;
  }

  .pt-md-35 {
    padding-top: 35px !important;
  }

  .pb-md-35 {
    padding-bottom: 35px !important;
  }

  .pl-md-35 {
    padding-left: 35px !important;
  }

  .pr-md-35 {
    padding-right: 35px !important;
  }

  .pt-md-40 {
    padding-top: 40px !important;
  }

  .pb-md-40 {
    padding-bottom: 40px !important;
  }

  .pl-md-40 {
    padding-left: 40px !important;
  }

  .pr-md-40 {
    padding-right: 40px !important;
  }

  .pt-md-45 {
    padding-top: 45px !important;
  }

  .pb-md-45 {
    padding-bottom: 45px !important;
  }

  .pl-md-45 {
    padding-left: 45px !important;
  }

  .pr-md-45 {
    padding-right: 45px !important;
  }

  .pt-md-50 {
    padding-top: 50px !important;
  }

  .pb-md-50 {
    padding-bottom: 50px !important;
  }

  .pl-md-50 {
    padding-left: 50px !important;
  }

  .pr-md-50 {
    padding-right: 50px !important;
  }

  .pt-md-55 {
    padding-top: 55px !important;
  }

  .pb-md-55 {
    padding-bottom: 55px !important;
  }

  .pl-md-55 {
    padding-left: 55px !important;
  }

  .pr-md-55 {
    padding-right: 55px !important;
  }

  .pt-md-60 {
    padding-top: 60px !important;
  }

  .pb-md-60 {
    padding-bottom: 60px !important;
  }

  .pl-md-60 {
    padding-left: 60px !important;
  }

  .pr-md-60 {
    padding-right: 60px !important;
  }

  .pt-md-65 {
    padding-top: 65px !important;
  }

  .pb-md-65 {
    padding-bottom: 65px !important;
  }

  .pl-md-65 {
    padding-left: 65px !important;
  }

  .pr-md-65 {
    padding-right: 65px !important;
  }

  .pt-md-70 {
    padding-top: 70px !important;
  }

  .pb-md-70 {
    padding-bottom: 70px !important;
  }

  .pl-md-70 {
    padding-left: 70px !important;
  }

  .pr-md-70 {
    padding-right: 70px !important;
  }

  .pt-md-75 {
    padding-top: 75px !important;
  }

  .pb-md-75 {
    padding-bottom: 75px !important;
  }

  .pl-md-75 {
    padding-left: 75px !important;
  }

  .pr-md-75 {
    padding-right: 75px !important;
  }

  .pt-md-80 {
    padding-top: 80px !important;
  }

  .pb-md-80 {
    padding-bottom: 80px !important;
  }

  .pl-md-80 {
    padding-left: 80px !important;
  }

  .pr-md-80 {
    padding-right: 80px !important;
  }

  .pt-md-85 {
    padding-top: 85px !important;
  }

  .pb-md-85 {
    padding-bottom: 85px !important;
  }

  .pl-md-85 {
    padding-left: 85px !important;
  }

  .pr-md-85 {
    padding-right: 85px !important;
  }

  .pt-md-90 {
    padding-top: 90px !important;
  }

  .pb-md-90 {
    padding-bottom: 90px !important;
  }

  .pl-md-90 {
    padding-left: 90px !important;
  }

  .pr-md-90 {
    padding-right: 90px !important;
  }

  .pt-md-95 {
    padding-top: 95px !important;
  }

  .pb-md-95 {
    padding-bottom: 95px !important;
  }

  .pl-md-95 {
    padding-left: 95px !important;
  }

  .pr-md-95 {
    padding-right: 95px !important;
  }

  .pt-md-100 {
    padding-top: 100px !important;
  }

  .pb-md-100 {
    padding-bottom: 100px !important;
  }

  .pl-md-100 {
    padding-left: 100px !important;
  }

  .pr-md-100 {
    padding-right: 100px !important;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .pt-sm-0 {
    padding-top: 0px !important;
  }

  .pb-sm-0 {
    padding-bottom: 0px !important;
  }

  .pl-sm-0 {
    padding-left: 0px !important;
  }

  .pr-sm-0 {
    padding-right: 0px !important;
  }

  .pt-sm-5 {
    padding-top: 5px !important;
  }

  .pb-sm-5 {
    padding-bottom: 5px !important;
  }

  .pl-sm-5 {
    padding-left: 5px !important;
  }

  .pr-sm-5 {
    padding-right: 5px !important;
  }

  .pt-sm-10 {
    padding-top: 10px !important;
  }

  .pb-sm-10 {
    padding-bottom: 10px !important;
  }

  .pl-sm-10 {
    padding-left: 10px !important;
  }

  .pr-sm-10 {
    padding-right: 10px !important;
  }

  .pt-sm-15 {
    padding-top: 15px !important;
  }

  .pb-sm-15 {
    padding-bottom: 15px !important;
  }

  .pl-sm-15 {
    padding-left: 15px !important;
  }

  .pr-sm-15 {
    padding-right: 15px !important;
  }

  .pt-sm-20 {
    padding-top: 20px !important;
  }

  .pb-sm-20 {
    padding-bottom: 20px !important;
  }

  .pl-sm-20 {
    padding-left: 20px !important;
  }

  .pr-sm-20 {
    padding-right: 20px !important;
  }

  .pt-sm-25 {
    padding-top: 25px !important;
  }

  .pb-sm-25 {
    padding-bottom: 25px !important;
  }

  .pl-sm-25 {
    padding-left: 25px !important;
  }

  .pr-sm-25 {
    padding-right: 25px !important;
  }

  .pt-sm-30 {
    padding-top: 30px !important;
  }

  .pb-sm-30 {
    padding-bottom: 30px !important;
  }

  .pl-sm-30 {
    padding-left: 30px !important;
  }

  .pr-sm-30 {
    padding-right: 30px !important;
  }

  .pt-sm-35 {
    padding-top: 35px !important;
  }

  .pb-sm-35 {
    padding-bottom: 35px !important;
  }

  .pl-sm-35 {
    padding-left: 35px !important;
  }

  .pr-sm-35 {
    padding-right: 35px !important;
  }

  .pt-sm-40 {
    padding-top: 40px !important;
  }

  .pb-sm-40 {
    padding-bottom: 40px !important;
  }

  .pl-sm-40 {
    padding-left: 40px !important;
  }

  .pr-sm-40 {
    padding-right: 40px !important;
  }

  .pt-sm-45 {
    padding-top: 45px !important;
  }

  .pb-sm-45 {
    padding-bottom: 45px !important;
  }

  .pl-sm-45 {
    padding-left: 45px !important;
  }

  .pr-sm-45 {
    padding-right: 45px !important;
  }

  .pt-sm-50 {
    padding-top: 50px !important;
  }

  .pb-sm-50 {
    padding-bottom: 50px !important;
  }

  .pl-sm-50 {
    padding-left: 50px !important;
  }

  .pr-sm-50 {
    padding-right: 50px !important;
  }

  .pt-sm-55 {
    padding-top: 55px !important;
  }

  .pb-sm-55 {
    padding-bottom: 55px !important;
  }

  .pl-sm-55 {
    padding-left: 55px !important;
  }

  .pr-sm-55 {
    padding-right: 55px !important;
  }

  .pt-sm-60 {
    padding-top: 60px !important;
  }

  .pb-sm-60 {
    padding-bottom: 60px !important;
  }

  .pl-sm-60 {
    padding-left: 60px !important;
  }

  .pr-sm-60 {
    padding-right: 60px !important;
  }

  .pt-sm-65 {
    padding-top: 65px !important;
  }

  .pb-sm-65 {
    padding-bottom: 65px !important;
  }

  .pl-sm-65 {
    padding-left: 65px !important;
  }

  .pr-sm-65 {
    padding-right: 65px !important;
  }

  .pt-sm-70 {
    padding-top: 70px !important;
  }

  .pb-sm-70 {
    padding-bottom: 70px !important;
  }

  .pl-sm-70 {
    padding-left: 70px !important;
  }

  .pr-sm-70 {
    padding-right: 70px !important;
  }

  .pt-sm-75 {
    padding-top: 75px !important;
  }

  .pb-sm-75 {
    padding-bottom: 75px !important;
  }

  .pl-sm-75 {
    padding-left: 75px !important;
  }

  .pr-sm-75 {
    padding-right: 75px !important;
  }

  .pt-sm-80 {
    padding-top: 80px !important;
  }

  .pb-sm-80 {
    padding-bottom: 80px !important;
  }

  .pl-sm-80 {
    padding-left: 80px !important;
  }

  .pr-sm-80 {
    padding-right: 80px !important;
  }

  .pt-sm-85 {
    padding-top: 85px !important;
  }

  .pb-sm-85 {
    padding-bottom: 85px !important;
  }

  .pl-sm-85 {
    padding-left: 85px !important;
  }

  .pr-sm-85 {
    padding-right: 85px !important;
  }

  .pt-sm-90 {
    padding-top: 90px !important;
  }

  .pb-sm-90 {
    padding-bottom: 90px !important;
  }

  .pl-sm-90 {
    padding-left: 90px !important;
  }

  .pr-sm-90 {
    padding-right: 90px !important;
  }

  .pt-sm-95 {
    padding-top: 95px !important;
  }

  .pb-sm-95 {
    padding-bottom: 95px !important;
  }

  .pl-sm-95 {
    padding-left: 95px !important;
  }

  .pr-sm-95 {
    padding-right: 95px !important;
  }

  .pt-sm-100 {
    padding-top: 100px !important;
  }

  .pb-sm-100 {
    padding-bottom: 100px !important;
  }

  .pl-sm-100 {
    padding-left: 100px !important;
  }

  .pr-sm-100 {
    padding-right: 100px !important;
  }
}
@media only screen and (max-width: 767px) {
  .pt-xs-0 {
    padding-top: 0px !important;
  }

  .pb-xs-0 {
    padding-bottom: 0px !important;
  }

  .pl-xs-0 {
    padding-left: 0px !important;
  }

  .pr-xs-0 {
    padding-right: 0px !important;
  }

  .pt-xs-5 {
    padding-top: 5px !important;
  }

  .pb-xs-5 {
    padding-bottom: 5px !important;
  }

  .pl-xs-5 {
    padding-left: 5px !important;
  }

  .pr-xs-5 {
    padding-right: 5px !important;
  }

  .pt-xs-10 {
    padding-top: 10px !important;
  }

  .pb-xs-10 {
    padding-bottom: 10px !important;
  }

  .pl-xs-10 {
    padding-left: 10px !important;
  }

  .pr-xs-10 {
    padding-right: 10px !important;
  }

  .pt-xs-15 {
    padding-top: 15px !important;
  }

  .pb-xs-15 {
    padding-bottom: 15px !important;
  }

  .pl-xs-15 {
    padding-left: 15px !important;
  }

  .pr-xs-15 {
    padding-right: 15px !important;
  }

  .pt-xs-20 {
    padding-top: 20px !important;
  }

  .pb-xs-20 {
    padding-bottom: 20px !important;
  }

  .pl-xs-20 {
    padding-left: 20px !important;
  }

  .pr-xs-20 {
    padding-right: 20px !important;
  }

  .pt-xs-25 {
    padding-top: 25px !important;
  }

  .pb-xs-25 {
    padding-bottom: 25px !important;
  }

  .pl-xs-25 {
    padding-left: 25px !important;
  }

  .pr-xs-25 {
    padding-right: 25px !important;
  }

  .pt-xs-30 {
    padding-top: 30px !important;
  }

  .pb-xs-30 {
    padding-bottom: 30px !important;
  }

  .pl-xs-30 {
    padding-left: 30px !important;
  }

  .pr-xs-30 {
    padding-right: 30px !important;
  }

  .pt-xs-35 {
    padding-top: 35px !important;
  }

  .pb-xs-35 {
    padding-bottom: 35px !important;
  }

  .pl-xs-35 {
    padding-left: 35px !important;
  }

  .pr-xs-35 {
    padding-right: 35px !important;
  }

  .pt-xs-40 {
    padding-top: 40px !important;
  }

  .pb-xs-40 {
    padding-bottom: 40px !important;
  }

  .pl-xs-40 {
    padding-left: 40px !important;
  }

  .pr-xs-40 {
    padding-right: 40px !important;
  }

  .pt-xs-45 {
    padding-top: 45px !important;
  }

  .pb-xs-45 {
    padding-bottom: 45px !important;
  }

  .pl-xs-45 {
    padding-left: 45px !important;
  }

  .pr-xs-45 {
    padding-right: 45px !important;
  }

  .pt-xs-50 {
    padding-top: 50px !important;
  }

  .pb-xs-50 {
    padding-bottom: 50px !important;
  }

  .pl-xs-50 {
    padding-left: 50px !important;
  }

  .pr-xs-50 {
    padding-right: 50px !important;
  }

  .pt-xs-55 {
    padding-top: 55px !important;
  }

  .pb-xs-55 {
    padding-bottom: 55px !important;
  }

  .pl-xs-55 {
    padding-left: 55px !important;
  }

  .pr-xs-55 {
    padding-right: 55px !important;
  }

  .pt-xs-60 {
    padding-top: 60px !important;
  }

  .pb-xs-60 {
    padding-bottom: 60px !important;
  }

  .pl-xs-60 {
    padding-left: 60px !important;
  }

  .pr-xs-60 {
    padding-right: 60px !important;
  }

  .pt-xs-65 {
    padding-top: 65px !important;
  }

  .pb-xs-65 {
    padding-bottom: 65px !important;
  }

  .pl-xs-65 {
    padding-left: 65px !important;
  }

  .pr-xs-65 {
    padding-right: 65px !important;
  }

  .pt-xs-70 {
    padding-top: 70px !important;
  }

  .pb-xs-70 {
    padding-bottom: 70px !important;
  }

  .pl-xs-70 {
    padding-left: 70px !important;
  }

  .pr-xs-70 {
    padding-right: 70px !important;
  }

  .pt-xs-75 {
    padding-top: 75px !important;
  }

  .pb-xs-75 {
    padding-bottom: 75px !important;
  }

  .pl-xs-75 {
    padding-left: 75px !important;
  }

  .pr-xs-75 {
    padding-right: 75px !important;
  }

  .pt-xs-80 {
    padding-top: 80px !important;
  }

  .pb-xs-80 {
    padding-bottom: 80px !important;
  }

  .pl-xs-80 {
    padding-left: 80px !important;
  }

  .pr-xs-80 {
    padding-right: 80px !important;
  }

  .pt-xs-85 {
    padding-top: 85px !important;
  }

  .pb-xs-85 {
    padding-bottom: 85px !important;
  }

  .pl-xs-85 {
    padding-left: 85px !important;
  }

  .pr-xs-85 {
    padding-right: 85px !important;
  }

  .pt-xs-90 {
    padding-top: 90px !important;
  }

  .pb-xs-90 {
    padding-bottom: 90px !important;
  }

  .pl-xs-90 {
    padding-left: 90px !important;
  }

  .pr-xs-90 {
    padding-right: 90px !important;
  }

  .pt-xs-95 {
    padding-top: 95px !important;
  }

  .pb-xs-95 {
    padding-bottom: 95px !important;
  }

  .pl-xs-95 {
    padding-left: 95px !important;
  }

  .pr-xs-95 {
    padding-right: 95px !important;
  }

  .pt-xs-100 {
    padding-top: 100px !important;
  }

  .pb-xs-100 {
    padding-bottom: 100px !important;
  }

  .pl-xs-100 {
    padding-left: 100px !important;
  }

  .pr-xs-100 {
    padding-right: 100px !important;
  }
}